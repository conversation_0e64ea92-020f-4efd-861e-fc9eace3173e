import { Timestamp } from 'rxjs';
import {
  ColumnState,
  RequestForm,
} from '../../common-services/interfaces/table-response';

// src/app/core/api/expenses.ts
export interface Expense {
  id: number;
  transaction_id: string;
  created_by: string;
  cost: number;
  description: string;
  date: Date;
  type: string;
  category: string;
  created_at: Date;
  updated_at: Date;
}
export interface ExpensesForm extends RequestForm {

}

export const initialExpenseColumnsState: ColumnState[] = [
  { key: 'cost', hidden: false },
  { key: 'description', hidden: false },
  { key: 'date', hidden: false },
  { key: 'type', hidden: false },
  { key: 'category', hidden: false },
  { key: 'created_at', hidden: false },
  { key: 'updated_at', hidden: false },
];
