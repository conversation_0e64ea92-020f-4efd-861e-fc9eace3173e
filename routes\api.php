<?php

use App\Http\Controllers\SeedController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/seed-all', [SeedController::class, 'seedAllModules']);
Route::delete('/delete-all', [SeedController::class, 'deleteAllData']);

// SAS Connection Routes
Route::prefix('sas')->group(function () {
    Route::post('/connections', [App\Http\Controllers\SasConnectionController::class, 'store']);
    Route::get('/connections', [App\Http\Controllers\SasConnectionController::class, 'index']);
    Route::post('/connections/test', [App\Http\Controllers\SasConnectionController::class, 'test']);
    Route::post('/connections/{id}/test', [App\Http\Controllers\SasConnectionController::class, 'test']);
    Route::post('/authenticate', [App\Http\Controllers\SasConnectionController::class, 'authenticate']);
});

// Auth Routes (compatible with existing structure)
Route::prefix('auth')->group(function () {
    Route::post('/login', [App\Http\Controllers\SasConnectionController::class, 'authenticate']);
});

// SAS4 Dynamic Access Routes
Route::prefix('sas4')->group(function () {
    Route::post('/connect', [App\Http\Controllers\Sas4DynamicAccessController::class, 'connectToSas4']);
    Route::post('/test-connection', [App\Http\Controllers\Sas4DynamicAccessController::class, 'testSas4Connection']);
    Route::post('/fetch-data', [App\Http\Controllers\Sas4DynamicAccessController::class, 'fetchSas4Data']);
    Route::get('/endpoints', [App\Http\Controllers\Sas4DynamicAccessController::class, 'getAvailableEndpoints']);
    Route::post('/test-encryption', [App\Http\Controllers\Sas4DynamicAccessController::class, 'testEncryption']);
});

