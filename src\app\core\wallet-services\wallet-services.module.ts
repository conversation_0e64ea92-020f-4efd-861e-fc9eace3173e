import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WalletApi } from './api/wallet.api';
import { WalletService, WalletServiceAbstract } from './services/wallet.service';

const API = [WalletApi];
const SERVICES = [
  {provide: WalletServiceAbstract, useClass: WalletService},
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class WalletServicesModule {
  static forRoot(): ModuleWithProviders<WalletServicesModule> {
    return {
      ngModule: WalletServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
