import 'package:flutter/foundation.dart';
import '../models/user_data_model.dart';
import '../models/add_user_model.dart';
import '../services/users_api_service.dart';
import '../../../core/services/sas_api_service.dart';

class UsersProvider extends ChangeNotifier {
  final UsersApiService _apiService = UsersApiService();
  SasApiService? _sasApiService;

  List<UserData> _users = [];
  UserStats? _userStats;
  List<UserData> _onlineUsers = [];
  bool _isLoading = false;
  String? _error;
  UserFilters _currentFilters = UserFilters();
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalUsers = 0;

  // Getters
  List<UserData> get users => _users;
  UserStats? get userStats => _userStats;
  List<UserData> get onlineUsers => _onlineUsers;
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserFilters get currentFilters => _currentFilters;
  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  int get totalUsers => _totalUsers;

  // تعيين معلومات الاتصال بـ SAS Radius
  void setSasRadiusConnection(String url, {String? token}) {
    // إنشاء SAS API service للاتصال الحقيقي
    _sasApiService = SasApiService(baseUrl: url);
    if (token != null) {
      _sasApiService!.setAuthToken(token);
    }

    // الاحتفاظ بالطريقة القديمة كـ fallback
    _apiService.setSasRadiusConnection(url, token: token);
  }

  // تحميل بيانات المستخدمين
  Future<void> loadUsers({UserFilters? filters, bool refresh = false}) async {
    if (refresh) {
      _users.clear();
      _currentPage = 1;
    }

    _setLoading(true);
    _clearError();

    try {
      final filtersToUse = filters ?? _currentFilters;
      final response = await _apiService.getAllUsers(filters: filtersToUse);

      if (refresh) {
        _users = response.data;
      } else {
        _users.addAll(response.data);
      }

      _currentFilters = filtersToUse;
      _currentPage = response.currentPage;
      _totalPages = response.totalPages;
      _totalUsers = response.total;

      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل بيانات المستخدمين: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل المزيد من البيانات (للتمرير اللانهائي)
  Future<void> loadMoreUsers() async {
    if (_isLoading || _currentPage >= _totalPages) return;

    final nextPageFilters = _currentFilters.copyWith(page: _currentPage + 1);
    await loadUsers(filters: nextPageFilters);
  }

  // البحث عن المستخدمين
  Future<void> searchUsers(String query) async {
    final searchFilters = UserFilters(search: query);
    await loadUsers(filters: searchFilters, refresh: true);
  }

  // تطبيق فلاتر
  Future<void> applyFilters(UserFilters filters) async {
    await loadUsers(filters: filters, refresh: true);
  }

  // تحميل إحصائيات المستخدمين
  Future<void> loadUserStats() async {
    try {
      _userStats = await _apiService.getUserStats();
      notifyListeners();
    } catch (e) {
      debugPrint('فشل في تحميل الإحصائيات: $e');
    }
  }

  // تحميل المستخدمين المتصلين
  Future<void> loadOnlineUsers() async {
    try {
      _onlineUsers = await _apiService.getOnlineUsers();
      notifyListeners();
    } catch (e) {
      debugPrint('فشل في تحميل المستخدمين المتصلين: $e');
    }
  }

  // جلب مستخدم محدد
  Future<UserData?> getUserById(String userId) async {
    try {
      return await _apiService.getUserById(userId);
    } catch (e) {
      _setError('فشل في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  // تحديث حالة المستخدم
  Future<bool> updateUserStatus(String userId, String status) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _apiService.updateUserStatus(userId, status);

      if (success) {
        // تحديث المستخدم في القائمة المحلية
        final userIndex = _users.indexWhere((user) => user.id == userId);
        if (userIndex != -1) {
          _users[userIndex] = _users[userIndex].copyWith(status: status);
          notifyListeners();
        }

        // إعادة تحميل الإحصائيات
        await loadUserStats();
      }

      return success;
    } catch (e) {
      _setError('فشل في تحديث حالة المستخدم: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // قطع اتصال المستخدم
  Future<bool> disconnectUser(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _apiService.disconnectUser(userId);

      if (success) {
        // إزالة المستخدم من قائمة المتصلين
        _onlineUsers.removeWhere((user) => user.id == userId);
        notifyListeners();

        // إعادة تحميل الإحصائيات
        await loadUserStats();
      }

      return success;
    } catch (e) {
      _setError('فشل في قطع اتصال المستخدم: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadUsers(refresh: true),
      loadUserStats(),
      loadOnlineUsers(),
    ]);
  }

  // فلترة المستخدمين حسب الحالة
  List<UserData> getUsersByStatus(String status) {
    return _users
        .where((user) => user.status.toLowerCase() == status.toLowerCase())
        .toList();
  }

  // الحصول على المستخدمين النشطين
  List<UserData> get activeUsers => getUsersByStatus('active');

  // الحصول على المستخدمين المعلقين
  List<UserData> get suspendedUsers => getUsersByStatus('suspended');

  // الحصول على المستخدمين المنتهيين
  List<UserData> get expiredUsers => getUsersByStatus('expired');

  // إعادة تعيين الفلاتر
  void resetFilters() {
    _currentFilters = UserFilters();
    loadUsers(refresh: true);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // إضافة مستخدم جديد
  Future<UserActionResponse> addUser(AddUserRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _apiService.addUser(request);

      if (response.success && response.user != null) {
        // إضافة المستخدم الجديد للقائمة المحلية
        _users.insert(0, response.user!);
        notifyListeners();

        // إعادة تحميل الإحصائيات
        await loadUserStats();
      }

      return response;
    } catch (e) {
      _setError('فشل في إضافة المستخدم: $e');
      return UserActionResponse(
        success: false,
        message: 'فشل في إضافة المستخدم: $e',
      );
    } finally {
      _setLoading(false);
    }
  }

  // تحديث مستخدم
  Future<UserActionResponse> updateUser(
    String userId,
    UpdateUserRequest request,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _apiService.updateUser(userId, request);

      if (response.success && response.user != null) {
        // تحديث المستخدم في القائمة المحلية
        final userIndex = _users.indexWhere((user) => user.id == userId);
        if (userIndex != -1) {
          _users[userIndex] = response.user!;
          notifyListeners();
        }

        // إعادة تحميل الإحصائيات
        await loadUserStats();
      }

      return response;
    } catch (e) {
      _setError('فشل في تحديث المستخدم: $e');
      return UserActionResponse(
        success: false,
        message: 'فشل في تحديث المستخدم: $e',
      );
    } finally {
      _setLoading(false);
    }
  }

  // حذف مستخدم
  Future<UserActionResponse> deleteUser(String userId) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _apiService.deleteUser(userId);

      if (response.success) {
        // حذف المستخدم من القائمة المحلية
        _users.removeWhere((user) => user.id == userId);
        notifyListeners();

        // إعادة تحميل الإحصائيات
        await loadUserStats();
      }

      return response;
    } catch (e) {
      _setError('فشل في حذف المستخدم: $e');
      return UserActionResponse(
        success: false,
        message: 'فشل في حذف المستخدم: $e',
      );
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على قائمة الخطط المتاحة
  List<SubscriptionPlan> getAvailablePlans() {
    return DefaultPlans.plans;
  }

  // الحصول على قائمة الحالات المتاحة
  List<UserStatus> getAvailableStatuses() {
    return UserStatus.values;
  }

  // تنظيف البيانات
  void clear() {
    _users.clear();
    _userStats = null;
    _onlineUsers.clear();
    _currentFilters = UserFilters();
    _currentPage = 1;
    _totalPages = 1;
    _totalUsers = 0;
    _clearError();
    notifyListeners();
  }
}
