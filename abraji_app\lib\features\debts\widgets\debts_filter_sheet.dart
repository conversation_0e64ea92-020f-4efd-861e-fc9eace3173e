import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/debt_models.dart';

class DebtsFilterSheet extends StatefulWidget {
  final DebtFilters currentFilters;
  final Function(DebtFilters) onApplyFilters;

  const DebtsFilterSheet({
    super.key,
    required this.currentFilters,
    required this.onApplyFilters,
  });

  @override
  State<DebtsFilterSheet> createState() => _DebtsFilterSheetState();
}

class _DebtsFilterSheetState extends State<DebtsFilterSheet> {
  late DebtFilters filters;

  @override
  void initState() {
    super.initState();
    filters = widget.currentFilters;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // المقبض
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'فلترة الديون',
                  style: AppTypography.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('إعادة تعيين'),
                ),
              ],
            ),
          ),

          const Divider(),

          // المحتوى
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الحالة
                  _buildSectionTitle('الحالة'),
                  _buildStatusFilter(),

                  const SizedBox(height: 24),

                  // النوع
                  _buildSectionTitle('النوع'),
                  _buildTypeFilter(),

                  const SizedBox(height: 24),

                  // المبلغ
                  _buildSectionTitle('المبلغ'),
                  _buildAmountFilter(),

                  const SizedBox(height: 24),

                  // التاريخ
                  _buildSectionTitle('التاريخ'),
                  _buildDateFilter(),

                  const SizedBox(height: 24),

                  // خيارات إضافية
                  _buildSectionTitle('خيارات إضافية'),
                  _buildAdditionalOptions(),
                ],
              ),
            ),
          ),

          // أزرار الإجراءات
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(top: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTypography.titleMedium.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildStatusFilter() {
    final statuses = [
      {'value': null, 'label': 'الكل'},
      {'value': 'pending', 'label': 'معلق'},
      {'value': 'paid', 'label': 'مدفوع'},
      {'value': 'overdue', 'label': 'متأخر'},
      {'value': 'cancelled', 'label': 'ملغي'},
    ];

    return Wrap(
      spacing: 8,
      children: statuses.map((status) {
        final isSelected = filters.status == status['value'];
        return FilterChip(
          label: Text(status['label'] as String),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              filters = filters.copyWith(
                status: selected ? status['value'] : null,
              );
            });
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
        );
      }).toList(),
    );
  }

  Widget _buildTypeFilter() {
    final types = [
      {'value': null, 'label': 'الكل'},
      {'value': 'service', 'label': 'خدمة'},
      {'value': 'penalty', 'label': 'غرامة'},
      {'value': 'other', 'label': 'أخرى'},
    ];

    return Wrap(
      spacing: 8,
      children: types.map((type) {
        final isSelected = filters.type == type['value'];
        return FilterChip(
          label: Text(type['label'] as String),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              filters = filters.copyWith(type: selected ? type['value'] : null);
            });
          },
          selectedColor: AppColors.secondary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.secondary,
        );
      }).toList(),
    );
  }

  Widget _buildAmountFilter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'الحد الأدنى',
                  suffixText: 'ر.س',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: filters.minAmount?.toString(),
                onChanged: (value) {
                  final amount = double.tryParse(value);
                  setState(() {
                    filters = filters.copyWith(minAmount: amount);
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'الحد الأقصى',
                  suffixText: 'ر.س',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: filters.maxAmount?.toString(),
                onChanged: (value) {
                  final amount = double.tryParse(value);
                  setState(() {
                    filters = filters.copyWith(maxAmount: amount);
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateFilter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'من تاريخ',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    filters.startDate != null
                        ? _formatDate(filters.startDate!)
                        : 'اختر التاريخ',
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'إلى تاريخ',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    filters.endDate != null
                        ? _formatDate(filters.endDate!)
                        : 'اختر التاريخ',
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdditionalOptions() {
    return Column(
      children: [
        CheckboxListTile(
          title: const Text('الديون المتأخرة فقط'),
          value: filters.isOverdue ?? false,
          onChanged: (value) {
            setState(() {
              filters = filters.copyWith(isOverdue: value);
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          filters = filters.copyWith(startDate: date);
        } else {
          filters = filters.copyWith(endDate: date);
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      filters = DebtFilters();
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(filters);
    Navigator.of(context).pop();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
