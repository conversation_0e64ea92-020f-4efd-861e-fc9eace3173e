.skeleton-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.skeleton {
  background-color: #ddd;
  border-radius: 4px;
  animation: skeleton-loading 1.2s infinite;
}

.skeleton-text {
  height: 20px;
  width: 100px;
}

.skeleton-chart {
  height: 400px;
  width: 100%;
}

@keyframes skeleton-loading {
  0% {
    background-color: #ddd;
  }
  50% {
    background-color: #eee;
  }
  100% {
    background-color: #ddd;
  }
}
