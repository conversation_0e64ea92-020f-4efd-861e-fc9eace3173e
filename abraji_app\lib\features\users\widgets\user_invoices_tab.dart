import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';

class UserInvoicesTab extends StatefulWidget {
  final String userId;

  const UserInvoicesTab({
    super.key,
    required this.userId,
  });

  @override
  State<UserInvoicesTab> createState() => _UserInvoicesTabState();
}

class _UserInvoicesTabState extends State<UserInvoicesTab> {
  List<Invoice> invoices = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    setState(() {
      isLoading = true;
    });

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      invoices = _generateMockInvoices();
      isLoading = false;
    });
  }

  List<Invoice> _generateMockInvoices() {
    return [
      Invoice(
        id: 'INV-001',
        amount: 100.0,
        status: 'paid',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        dueDate: DateTime.now().subtract(const Duration(days: 1)),
        paidAt: DateTime.now().subtract(const Duration(days: 2)),
        description: 'اشتراك شهري - خطة مميزة',
        planName: 'خطة مميزة',
      ),
      Invoice(
        id: 'INV-002',
        amount: 50.0,
        status: 'pending',
        createdAt: DateTime.now().subtract(const Duration(days: 35)),
        dueDate: DateTime.now().add(const Duration(days: 5)),
        description: 'رسوم إضافية - استهلاك زائد',
        planName: 'رسوم إضافية',
      ),
      Invoice(
        id: 'INV-003',
        amount: 100.0,
        status: 'overdue',
        createdAt: DateTime.now().subtract(const Duration(days: 65)),
        dueDate: DateTime.now().subtract(const Duration(days: 10)),
        description: 'اشتراك شهري - خطة مميزة',
        planName: 'خطة مميزة',
      ),
      Invoice(
        id: 'INV-004',
        amount: 75.0,
        status: 'paid',
        createdAt: DateTime.now().subtract(const Duration(days: 95)),
        dueDate: DateTime.now().subtract(const Duration(days: 70)),
        paidAt: DateTime.now().subtract(const Duration(days: 68)),
        description: 'اشتراك شهري - خطة أساسية',
        planName: 'خطة أساسية',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadInvoices,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات الفواتير
            _buildInvoiceStats(),
            
            const SizedBox(height: 20),
            
            // أزرار الإجراءات
            _buildActionButtons(),
            
            const SizedBox(height: 20),
            
            // قائمة الفواتير
            Text(
              'فواتير المستخدم',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (invoices.isEmpty)
              _buildEmptyState()
            else
              ...invoices.map((invoice) => _buildInvoiceCard(invoice)),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceStats() {
    final totalAmount = invoices.fold<double>(0, (sum, invoice) => sum + invoice.amount);
    final paidAmount = invoices
        .where((invoice) => invoice.status == 'paid')
        .fold<double>(0, (sum, invoice) => sum + invoice.amount);
    final pendingAmount = invoices
        .where((invoice) => invoice.status == 'pending')
        .fold<double>(0, (sum, invoice) => sum + invoice.amount);
    final overdueAmount = invoices
        .where((invoice) => invoice.status == 'overdue')
        .fold<double>(0, (sum, invoice) => sum + invoice.amount);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.receipt,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات الفواتير',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الفواتير',
                    '${totalAmount.toStringAsFixed(2)} ر.س',
                    Icons.receipt_long,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المدفوع',
                    '${paidAmount.toStringAsFixed(2)} ر.س',
                    Icons.check_circle,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'المعلق',
                    '${pendingAmount.toStringAsFixed(2)} ر.س',
                    Icons.pending,
                    AppColors.warning,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المتأخر',
                    '${overdueAmount.toStringAsFixed(2)} ر.س',
                    Icons.error,
                    AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _createNewInvoice,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء فاتورة جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _exportInvoices,
            icon: const Icon(Icons.download),
            label: const Text('تصدير الفواتير'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invoice.id,
                        style: AppTypography.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        invoice.description,
                        style: AppTypography.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(invoice.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(invoice.status),
                    style: AppTypography.bodySmall.copyWith(
                      color: _getStatusColor(invoice.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInvoiceInfo(
                    'المبلغ',
                    '${invoice.amount.toStringAsFixed(2)} ر.س',
                    Icons.attach_money,
                  ),
                ),
                Expanded(
                  child: _buildInvoiceInfo(
                    'تاريخ الإنشاء',
                    _formatDate(invoice.createdAt),
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInvoiceInfo(
                    'تاريخ الاستحقاق',
                    _formatDate(invoice.dueDate),
                    Icons.event,
                  ),
                ),
                Expanded(
                  child: _buildInvoiceInfo(
                    'تاريخ الدفع',
                    invoice.paidAt != null ? _formatDate(invoice.paidAt!) : 'لم يتم الدفع',
                    Icons.payment,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (invoice.status == 'pending' || invoice.status == 'overdue') ...[
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _markAsPaid(invoice.id),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تسديد'),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _viewInvoiceDetails(invoice),
                    child: const Text('عرض التفاصيل'),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _downloadInvoice(invoice.id),
                  icon: const Icon(Icons.download),
                  tooltip: 'تحميل الفاتورة',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceInfo(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: AppTypography.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            Icons.receipt_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فواتير',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم إنشاء أي فواتير لهذا المستخدم حتى الآن',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'paid':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'overdue':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'paid':
        return 'مدفوع';
      case 'pending':
        return 'معلق';
      case 'overdue':
        return 'متأخر';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _createNewInvoice() {
    // تنفيذ إنشاء فاتورة جديدة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إنشاء فاتورة جديدة')),
    );
  }

  void _exportInvoices() {
    // تنفيذ تصدير الفواتير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تصدير الفواتير')),
    );
  }

  void _markAsPaid(String invoiceId) {
    setState(() {
      final invoiceIndex = invoices.indexWhere((inv) => inv.id == invoiceId);
      if (invoiceIndex != -1) {
        invoices[invoiceIndex] = invoices[invoiceIndex].copyWith(
          status: 'paid',
          paidAt: DateTime.now(),
        );
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تسديد الفاتورة بنجاح')),
    );
  }

  void _viewInvoiceDetails(Invoice invoice) {
    // عرض تفاصيل الفاتورة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الفاتورة ${invoice.id}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الوصف: ${invoice.description}'),
            Text('المبلغ: ${invoice.amount.toStringAsFixed(2)} ر.س'),
            Text('الحالة: ${_getStatusText(invoice.status)}'),
            Text('تاريخ الإنشاء: ${_formatDate(invoice.createdAt)}'),
            Text('تاريخ الاستحقاق: ${_formatDate(invoice.dueDate)}'),
            if (invoice.paidAt != null)
              Text('تاريخ الدفع: ${_formatDate(invoice.paidAt!)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _downloadInvoice(String invoiceId) {
    // تنفيذ تحميل الفاتورة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم تحميل الفاتورة $invoiceId')),
    );
  }
}

// نموذج بيانات الفاتورة
class Invoice {
  final String id;
  final double amount;
  final String status;
  final DateTime createdAt;
  final DateTime dueDate;
  final DateTime? paidAt;
  final String description;
  final String planName;

  Invoice({
    required this.id,
    required this.amount,
    required this.status,
    required this.createdAt,
    required this.dueDate,
    this.paidAt,
    required this.description,
    required this.planName,
  });

  Invoice copyWith({
    String? id,
    double? amount,
    String? status,
    DateTime? createdAt,
    DateTime? dueDate,
    DateTime? paidAt,
    String? description,
    String? planName,
  }) {
    return Invoice(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      dueDate: dueDate ?? this.dueDate,
      paidAt: paidAt ?? this.paidAt,
      description: description ?? this.description,
      planName: planName ?? this.planName,
    );
  }
}
