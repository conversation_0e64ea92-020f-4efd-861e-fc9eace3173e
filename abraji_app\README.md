# AbrajiAPIs Flutter App

تطبيق Flutter جميل للإدارة المالية مع واجهة مستخدم عصرية وتجربة مستخدم متميزة.

## المميزات

### 🎨 تصميم جميل وعصري
- واجهة مستخدم حديثة مع ألوان متدرجة جذابة
- تصميم متجاوب يعمل على جميع الأحجام
- أنيميشن سلس وتفاعلي
- دعم الوضع المظلم والفاتح

### 🔐 نظام مصادقة متكامل
- تسجيل دخول وإنشاء حساب جديد
- استعادة كلمة المرور
- تسجيل دخول اجتماعي (Google, Facebook)
- حفظ حالة تسجيل الدخول

### 📊 لوحة تحكم تفاعلية
- عرض الرصيد الحالي مع إمكانية إخفاء/إظهار
- إحصائيات سريعة للمعاملات المالية
- رسوم بيانية تفاعلية للدخل والمصروفات
- قائمة المعاملات الحديثة

### 🧭 تنقل سهل ومرن
- شريط تنقل سفلي جميل
- زر إجراءات سريعة عائم
- نظام توجيه متقدم مع GoRouter
- انتقالات سلسة بين الشاشات

## التشغيل

### متطلبات النظام
- Flutter SDK 3.0+
- Dart 3.0+
- Chrome (للتشغيل على الويب)

### خطوات التشغيل

1. **تثبيت التبعيات**
```bash
flutter pub get
```

2. **تشغيل التطبيق**
```bash
# للتشغيل على الويب
flutter run -d chrome --web-port=8080

# للتشغيل على الهاتف
flutter run
```

## الشاشات المتاحة

### 🚀 شاشات البداية
- **Splash Screen**: شاشة البداية مع شعار التطبيق
- **Onboarding**: شاشات التعريف بالتطبيق (3 شاشات)

### 🔑 شاشات المصادقة
- **تسجيل الدخول**: مع دعم التسجيل الاجتماعي
- **إنشاء حساب**: تسجيل مستخدم جديد
- **استعادة كلمة المرور**: إعادة تعيين كلمة المرور

### 📱 الشاشات الرئيسية
- **لوحة التحكم**: الشاشة الرئيسية مع الإحصائيات
- **المعاملات**: قائمة وتفاصيل المعاملات
- **المحفظة**: إدارة المحافظ المالية
- **الملف الشخصي**: معلومات المستخدم
- **الإعدادات**: إعدادات التطبيق

تم تطوير هذا التطبيق بـ ❤️ باستخدام Flutter
