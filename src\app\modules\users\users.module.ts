import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UsersRoutingModule } from './users-routing.module';
import { AllUsersComponent } from './all-users/all-users.component';
import { OnlineUsersComponent } from './online-users/online-users.component';
import { UserDetailsComponent } from './user-details/user-details.component';
import { SharedModule } from '../shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserTrafficComponent } from './user-traffic/user-traffic.component';
import { UserEditComponent } from './user-edit/user-edit.component';
import { UserOverviewComponent } from './user-overview/user-overview.component';
import { UserSessionsComponent } from './user-sessions/user-sessions.component';
import { UserInvoicesComponent } from './user-invoices/user-invoices.component';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { ChartModule } from 'angular-highcharts';
import { TranslocoModule } from '@jsverse/transloco';
import { TableSkeletonComponent } from '../shared/skeletons/table/table.component';
import { CreateUserComponent } from './create-user/create-user.component';
import { QrCodeModule } from 'ng-qrcode';
import { UserQrComponent } from './user-qr/user-qr.component';
import * as CryptoJS from 'crypto-js';
import { ActivateUserComponent } from './activate-user/activate-user.component';
import { EditProfileComponent } from './edit-profile/edit-profile.component';

@NgModule({
  declarations: [
    AllUsersComponent,
    OnlineUsersComponent,
    UserDetailsComponent,
    UserTrafficComponent,
    UserEditComponent,
    UserOverviewComponent,
    UserSessionsComponent,
    UserInvoicesComponent,
    CreateUserComponent,
    EditProfileComponent,
    UserQrComponent,
    ActivateUserComponent,
  ],
  imports: [
    CommonModule,
    UsersRoutingModule,
    SharedModule,
    FormsModule,
    RouterLink,
    RouterLinkActive,
    ChartModule,
    TranslocoModule,
    TableSkeletonComponent,
    ReactiveFormsModule,
    QrCodeModule
  ],
})
export class UsersModule {}
