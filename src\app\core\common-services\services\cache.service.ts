import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CacheService {
  private cacheClearCallbacks: Array<() => void> = [];

  registerCacheClearCallback(callback: () => void): void {
    this.cacheClearCallbacks.push(callback);
  }

  clearAllCaches(): void {
    this.cacheClearCallbacks.forEach((callback) => callback());
    console.log('Cleared all caches');

  }
}
