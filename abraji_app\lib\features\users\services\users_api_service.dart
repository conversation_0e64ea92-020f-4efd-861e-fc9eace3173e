import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user_data_model.dart';
import '../models/add_user_model.dart';

class UsersApiService {
  String? _sasRadiusUrl;
  String? _authToken;

  // تعيين معلومات الاتصال
  void setSasRadiusConnection(String url, {String? token}) {
    _sasRadiusUrl = url;
    _authToken = token;
  }

  // Headers للطلبات
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  // جلب جميع المستخدمين
  Future<UsersResponse> getAllUsers({UserFilters? filters}) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    // قائمة من endpoints محتملة لجلب المستخدمين
    final endpoints = [
      '/api/users',
      '/api/subscribers',
      '/api/radius/users',
      '/users',
      '/subscribers',
      '/api/clients',
      '/clients',
    ];

    for (final endpoint in endpoints) {
      try {
        final queryParams = filters?.toQueryParams() ?? {};
        final uri = Uri.parse('$_sasRadiusUrl$endpoint').replace(
          queryParameters: queryParams.map(
            (key, value) => MapEntry(key, value.toString()),
          ),
        );

        final response = await http
            .get(uri, headers: _headers)
            .timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          final jsonData = json.decode(response.body);
          return UsersResponse.fromJson(jsonData);
        } else if (response.statusCode != 404) {
          // إذا لم يكن 404، فهناك خطأ آخر
          throw Exception(
            'فشل في جلب بيانات المستخدمين: ${response.statusCode}',
          );
        }
        // إذا كان 404، نجرب endpoint التالي
      } catch (e) {
        if (e.toString().contains('404')) {
          continue; // نجرب endpoint التالي
        }
        // في حالة أي خطأ آخر، نستخدم البيانات التجريبية
        break;
      }
    }

    // في حالة عدم توفر API، إرجاع بيانات تجريبية
    return _getMockUsersData(filters);
  }

  // إضافة مستخدم جديد
  Future<UserActionResponse> addUser(AddUserRequest request) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    // قائمة من endpoints محتملة لإضافة المستخدمين
    final endpoints = [
      '/api/users',
      '/api/subscribers',
      '/api/radius/users',
      '/users',
      '/subscribers',
      '/api/clients',
      '/clients',
    ];

    for (final endpoint in endpoints) {
      try {
        final response = await http
            .post(
              Uri.parse('$_sasRadiusUrl$endpoint'),
              headers: _headers,
              body: json.encode(request.toJson()),
            )
            .timeout(const Duration(seconds: 30));

        if (response.statusCode == 200 || response.statusCode == 201) {
          final jsonData = json.decode(response.body);
          return UserActionResponse.fromJson(jsonData);
        } else if (response.statusCode != 404) {
          throw Exception('فشل في إضافة المستخدم: ${response.statusCode}');
        }
      } catch (e) {
        if (e.toString().contains('404')) {
          continue;
        }
        break;
      }
    }

    // في حالة عدم توفر API، إرجاع استجابة تجريبية
    return _getMockAddUserResponse(request);
  }

  // تحديث مستخدم
  Future<UserActionResponse> updateUser(
    String userId,
    UpdateUserRequest request,
  ) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    final endpoints = [
      '/api/users/$userId',
      '/api/subscribers/$userId',
      '/api/radius/users/$userId',
      '/users/$userId',
      '/subscribers/$userId',
    ];

    for (final endpoint in endpoints) {
      try {
        final response = await http
            .put(
              Uri.parse('$_sasRadiusUrl$endpoint'),
              headers: _headers,
              body: json.encode(request.toJson()),
            )
            .timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          final jsonData = json.decode(response.body);
          return UserActionResponse.fromJson(jsonData);
        } else if (response.statusCode != 404) {
          throw Exception('فشل في تحديث المستخدم: ${response.statusCode}');
        }
      } catch (e) {
        if (e.toString().contains('404')) {
          continue;
        }
        break;
      }
    }

    return _getMockUpdateUserResponse(userId, request);
  }

  // حذف مستخدم
  Future<UserActionResponse> deleteUser(String userId) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    final endpoints = [
      '/api/users/$userId',
      '/api/subscribers/$userId',
      '/api/radius/users/$userId',
      '/users/$userId',
      '/subscribers/$userId',
    ];

    for (final endpoint in endpoints) {
      try {
        final response = await http
            .delete(Uri.parse('$_sasRadiusUrl$endpoint'), headers: _headers)
            .timeout(const Duration(seconds: 30));

        if (response.statusCode == 200 || response.statusCode == 204) {
          return UserActionResponse(
            success: true,
            message: 'تم حذف المستخدم بنجاح',
          );
        } else if (response.statusCode != 404) {
          throw Exception('فشل في حذف المستخدم: ${response.statusCode}');
        }
      } catch (e) {
        if (e.toString().contains('404')) {
          continue;
        }
        break;
      }
    }

    return UserActionResponse(
      success: true,
      message: 'تم حذف المستخدم بنجاح (محاكاة)',
    );
  }

  // جلب مستخدم محدد
  Future<UserData?> getUserById(String userId) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    try {
      final response = await http
          .get(Uri.parse('$_sasRadiusUrl/api/users/$userId'), headers: _headers)
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return UserData.fromJson(jsonData['data']);
        }
        return null;
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('فشل في جلب بيانات المستخدم: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // البحث عن المستخدمين
  Future<UsersResponse> searchUsers(
    String query, {
    UserFilters? filters,
  }) async {
    final searchFilters = (filters ?? UserFilters()).copyWith(search: query);
    return getAllUsers(filters: searchFilters);
  }

  // جلب إحصائيات المستخدمين
  Future<UserStats> getUserStats() async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    try {
      final response = await http
          .get(Uri.parse('$_sasRadiusUrl/api/users/stats'), headers: _headers)
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return UserStats.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? 'فشل في جلب الإحصائيات');
        }
      } else {
        throw Exception('فشل في جلب الإحصائيات: ${response.statusCode}');
      }
    } catch (e) {
      // في حالة عدم توفر API، إرجاع إحصائيات تجريبية
      return _getMockUserStats();
    }
  }

  // جلب المستخدمين المتصلين حالياً
  Future<List<UserData>> getOnlineUsers() async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    try {
      final response = await http
          .get(Uri.parse('$_sasRadiusUrl/api/users/online'), headers: _headers)
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return (jsonData['data'] as List)
              .map((item) => UserData.fromJson(item))
              .toList();
        }
        return [];
      } else {
        throw Exception(
          'فشل في جلب المستخدمين المتصلين: ${response.statusCode}',
        );
      }
    } catch (e) {
      return []; // إرجاع قائمة فارغة في حالة الخطأ
    }
  }

  // تحديث حالة المستخدم
  Future<bool> updateUserStatus(String userId, String status) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    try {
      final response = await http
          .patch(
            Uri.parse('$_sasRadiusUrl/api/users/$userId/status'),
            headers: _headers,
            body: json.encode({'status': status}),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      throw Exception('خطأ في تحديث حالة المستخدم: $e');
    }
  }

  // قطع اتصال المستخدم
  Future<bool> disconnectUser(String userId) async {
    if (_sasRadiusUrl == null) {
      throw Exception('لم يتم تعيين رابط SAS Radius');
    }

    try {
      final response = await http
          .post(
            Uri.parse('$_sasRadiusUrl/api/users/$userId/disconnect'),
            headers: _headers,
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      throw Exception('خطأ في قطع اتصال المستخدم: $e');
    }
  }

  // بيانات تجريبية للاختبار
  Future<UsersResponse> _getMockUsersData(UserFilters? filters) async {
    await Future.delayed(const Duration(seconds: 1)); // محاكاة تأخير الشبكة

    final mockUsers = [
      UserData(
        id: '1',
        username: 'user001',
        fullName: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        status: 'active',
        plan: 'Premium',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        lastLogin: DateTime.now().subtract(const Duration(hours: 2)),
        ipAddress: '*************',
        balance: 150.0,
      ),
      UserData(
        id: '2',
        username: 'user002',
        fullName: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '+966507654321',
        status: 'active',
        plan: 'Basic',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        expiryDate: DateTime.now().add(const Duration(days: 45)),
        lastLogin: DateTime.now().subtract(const Duration(minutes: 30)),
        ipAddress: '*************',
        balance: 75.0,
      ),
      UserData(
        id: '3',
        username: 'user003',
        fullName: 'محمد سالم',
        email: '<EMAIL>',
        phone: '+966509876543',
        status: 'suspended',
        plan: 'Premium',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        expiryDate: DateTime.now().add(const Duration(days: 10)),
        lastLogin: DateTime.now().subtract(const Duration(days: 5)),
        ipAddress: '*************',
        balance: 0.0,
      ),
      UserData(
        id: '4',
        username: 'user004',
        fullName: 'نورا أحمد',
        email: '<EMAIL>',
        phone: '+966502468135',
        status: 'expired',
        plan: 'Basic',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        expiryDate: DateTime.now().subtract(const Duration(days: 5)),
        lastLogin: DateTime.now().subtract(const Duration(days: 10)),
        ipAddress: '*************',
        balance: 25.0,
      ),
    ];

    // تطبيق الفلاتر
    var filteredUsers = mockUsers;
    if (filters?.search != null && filters!.search!.isNotEmpty) {
      filteredUsers = filteredUsers
          .where(
            (user) =>
                user.username.toLowerCase().contains(
                  filters.search!.toLowerCase(),
                ) ||
                (user.fullName?.toLowerCase().contains(
                      filters.search!.toLowerCase(),
                    ) ??
                    false),
          )
          .toList();
    }

    if (filters?.status != null && filters!.status!.isNotEmpty) {
      filteredUsers = filteredUsers
          .where((user) => user.status == filters.status)
          .toList();
    }

    return UsersResponse(
      success: true,
      message: 'تم جلب البيانات بنجاح',
      data: filteredUsers,
      total: filteredUsers.length,
      currentPage: filters?.page ?? 1,
      totalPages: 1,
    );
  }

  // إحصائيات تجريبية
  UserStats _getMockUserStats() {
    return UserStats(
      totalUsers: 150,
      activeUsers: 120,
      inactiveUsers: 15,
      suspendedUsers: 10,
      expiredUsers: 5,
      totalBalance: 12500.0,
      onlineUsers: 45,
    );
  }

  // استجابة تجريبية لإضافة مستخدم
  UserActionResponse _getMockAddUserResponse(AddUserRequest request) {
    final newUser = UserData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      username: request.username,
      fullName: request.fullName,
      email: request.email,
      phone: request.phone,
      address: request.address,
      status: request.status,
      plan: request.plan ?? 'Basic',
      createdAt: DateTime.now(),
      expiryDate:
          request.expiryDate ?? DateTime.now().add(const Duration(days: 30)),
      balance: request.balance ?? 0.0,
      notes: request.notes,
      additionalData: request.additionalData,
    );

    return UserActionResponse(
      success: true,
      message: 'تم إضافة المستخدم بنجاح',
      user: newUser,
    );
  }

  // استجابة تجريبية لتحديث مستخدم
  UserActionResponse _getMockUpdateUserResponse(
    String userId,
    UpdateUserRequest request,
  ) {
    // البحث عن المستخدم في البيانات التجريبية - استخدام البيانات المباشرة
    final mockUsers = [
      UserData(
        id: '1',
        username: 'user001',
        fullName: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        status: 'active',
        plan: 'Premium',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        balance: 150.0,
      ),
      UserData(
        id: '2',
        username: 'user002',
        fullName: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '+966507654321',
        status: 'active',
        plan: 'Basic',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        expiryDate: DateTime.now().add(const Duration(days: 45)),
        balance: 75.0,
      ),
    ];
    final existingUser = mockUsers.firstWhere(
      (user) => user.id == userId,
      orElse: () => mockUsers.first,
    );

    final updatedUser = existingUser.copyWith(
      fullName: request.fullName,
      email: request.email,
      phone: request.phone,
      address: request.address,
      status: request.status,
      plan: request.plan,
      expiryDate: request.expiryDate,
      balance: request.balance,
      notes: request.notes,
      additionalData: request.additionalData,
    );

    return UserActionResponse(
      success: true,
      message: 'تم تحديث المستخدم بنجاح',
      user: updatedUser,
    );
  }
}
