import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/providers/language_provider.dart';

class SimpleSettingsScreen extends StatefulWidget {
  const SimpleSettingsScreen({super.key});

  @override
  State<SimpleSettingsScreen> createState() => _SimpleSettingsScreenState();
}

class _SimpleSettingsScreenState extends State<SimpleSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // إعدادات الإشعارات
  bool _pushNotifications = true;
  bool _emailNotifications = false;
  bool _smsNotifications = true;
  bool _transactionAlerts = true;
  bool _securityAlerts = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadNotificationSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadNotificationSettings() {
    // تحميل إعدادات الإشعارات من SharedPreferences
    // يمكن تطبيق هذا لاحقاً
  }

  void _saveNotificationSettings() {
    // حفظ إعدادات الإشعارات في SharedPreferences
    // يمكن تطبيق هذا لاحقاً
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LanguageProvider>(
          builder: (context, languageProvider, child) {
            return Text(
              languageProvider.isArabic ? 'الإعدادات' : 'Settings',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.currentFontFamily,
              ),
            );
          },
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: [
            Consumer<LanguageProvider>(
              builder: (context, languageProvider, child) {
                return Tab(
                  text: languageProvider.isArabic ? 'اللغة' : 'Language',
                );
              },
            ),
            Consumer<LanguageProvider>(
              builder: (context, languageProvider, child) {
                return Tab(
                  text: languageProvider.isArabic
                      ? 'الإشعارات'
                      : 'Notifications',
                );
              },
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildLanguageTab(), _buildNotificationsTab()],
      ),
    );
  }

  // تبويب إعدادات اللغة
  Widget _buildLanguageTab() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.isArabic
                    ? 'اختر لغة التطبيق'
                    : 'Choose App Language',
                style: AppTypography.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                languageProvider.isArabic
                    ? 'اختر اللغة المفضلة لديك لواجهة التطبيق'
                    : 'Choose your preferred language for the app interface',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 24),

              // خيارات اللغة
              _buildLanguageOption(
                languageProvider: languageProvider,
                title: languageProvider.isArabic ? 'العربية' : 'Arabic',
                subtitle: 'العربية - السعودية',
                isSelected: languageProvider.isArabic,
                onTap: () => languageProvider.setArabic(),
                fontFamily: LanguageProvider.arabicFont,
              ),

              const SizedBox(height: 12),

              _buildLanguageOption(
                languageProvider: languageProvider,
                title: languageProvider.isArabic ? 'الإنجليزية' : 'English',
                subtitle: 'English - United States',
                isSelected: languageProvider.isEnglish,
                onTap: () => languageProvider.setEnglish(),
                fontFamily: LanguageProvider.englishFont,
              ),

              const SizedBox(height: 24),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.info.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.info,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          languageProvider.isArabic ? 'معلومات' : 'Information',
                          style: AppTypography.titleSmall.copyWith(
                            color: AppColors.info,
                            fontWeight: FontWeight.bold,
                            fontFamily: languageProvider.currentFontFamily,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageProvider.isArabic
                          ? '• سيتم تطبيق اللغة الجديدة على جميع أجزاء التطبيق\n'
                                '• سيتم تغيير نوع الخط تلقائياً حسب اللغة المختارة\n'
                                '• العربية: خط Cairo\n'
                                '• الإنجليزية: خط Roboto'
                          : '• The new language will be applied to all parts of the app\n'
                                '• Font type will change automatically based on selected language\n'
                                '• Arabic: Cairo Font\n'
                                '• English: Roboto Font',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.info,
                        height: 1.5,
                        fontFamily: languageProvider.currentFontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption({
    required LanguageProvider languageProvider,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
    required String fontFamily,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppColors.primary
              : AppColors.textTertiary.withValues(alpha: 0.3),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          title,
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            fontFamily: fontFamily,
            color: isSelected ? AppColors.primary : AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontFamily: fontFamily,
          ),
        ),
        trailing: isSelected
            ? Icon(Icons.check_circle, color: AppColors.primary, size: 24)
            : Icon(
                Icons.radio_button_unchecked,
                color: AppColors.textTertiary,
                size: 24,
              ),
        onTap: onTap,
      ),
    );
  }

  // تبويب إعدادات الإشعارات
  Widget _buildNotificationsTab() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.isArabic
                    ? 'إعدادات الإشعارات'
                    : 'Notification Settings',
                style: AppTypography.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                languageProvider.isArabic
                    ? 'تحكم في الإشعارات التي تريد استلامها'
                    : 'Control which notifications you want to receive',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 24),

              // إشعارات عامة
              _buildNotificationSection(
                languageProvider: languageProvider,
                title: languageProvider.isArabic
                    ? 'الإشعارات العامة'
                    : 'General Notifications',
                children: [
                  _buildNotificationTile(
                    languageProvider: languageProvider,
                    title: languageProvider.isArabic
                        ? 'الإشعارات المدفوعة'
                        : 'Push Notifications',
                    subtitle: languageProvider.isArabic
                        ? 'استلام الإشعارات على الجهاز'
                        : 'Receive notifications on device',
                    value: _pushNotifications,
                    onChanged: (value) {
                      setState(() {
                        _pushNotifications = value;
                      });
                      _saveNotificationSettings();
                    },
                  ),
                  _buildNotificationTile(
                    languageProvider: languageProvider,
                    title: languageProvider.isArabic
                        ? 'إشعارات البريد الإلكتروني'
                        : 'Email Notifications',
                    subtitle: languageProvider.isArabic
                        ? 'استلام الإشعارات عبر البريد الإلكتروني'
                        : 'Receive notifications via email',
                    value: _emailNotifications,
                    onChanged: (value) {
                      setState(() {
                        _emailNotifications = value;
                      });
                      _saveNotificationSettings();
                    },
                  ),
                  _buildNotificationTile(
                    languageProvider: languageProvider,
                    title: languageProvider.isArabic
                        ? 'الرسائل النصية'
                        : 'SMS Notifications',
                    subtitle: languageProvider.isArabic
                        ? 'استلام الإشعارات عبر الرسائل النصية'
                        : 'Receive notifications via SMS',
                    value: _smsNotifications,
                    onChanged: (value) {
                      setState(() {
                        _smsNotifications = value;
                      });
                      _saveNotificationSettings();
                    },
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // إشعارات متخصصة
              _buildNotificationSection(
                languageProvider: languageProvider,
                title: languageProvider.isArabic
                    ? 'إشعارات متخصصة'
                    : 'Specialized Notifications',
                children: [
                  _buildNotificationTile(
                    languageProvider: languageProvider,
                    title: languageProvider.isArabic
                        ? 'تنبيهات المعاملات'
                        : 'Transaction Alerts',
                    subtitle: languageProvider.isArabic
                        ? 'إشعارات عند إجراء معاملات جديدة'
                        : 'Notifications for new transactions',
                    value: _transactionAlerts,
                    onChanged: (value) {
                      setState(() {
                        _transactionAlerts = value;
                      });
                      _saveNotificationSettings();
                    },
                  ),
                  _buildNotificationTile(
                    languageProvider: languageProvider,
                    title: languageProvider.isArabic
                        ? 'تنبيهات الأمان'
                        : 'Security Alerts',
                    subtitle: languageProvider.isArabic
                        ? 'إشعارات الأمان والدخول للحساب'
                        : 'Security and login notifications',
                    value: _securityAlerts,
                    onChanged: (value) {
                      setState(() {
                        _securityAlerts = value;
                      });
                      _saveNotificationSettings();
                    },
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // إعدادات المطور
              _buildNotificationSection(
                languageProvider: languageProvider,
                title: languageProvider.isArabic
                    ? 'إعدادات المطور'
                    : 'Developer Settings',
                children: [
                  ListTile(
                    leading: Icon(
                      Icons.bug_report_outlined,
                      color: AppColors.warning,
                    ),
                    title: Text(
                      languageProvider.isArabic ? 'اختبار API' : 'API Test',
                      style: AppTypography.bodyLarge.copyWith(
                        fontWeight: FontWeight.w500,
                        fontFamily: languageProvider.currentFontFamily,
                      ),
                    ),
                    subtitle: Text(
                      languageProvider.isArabic
                          ? 'اختبار الاتصال مع الخادم'
                          : 'Test server connection',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                        fontFamily: languageProvider.currentFontFamily,
                      ),
                    ),
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    onTap: () {
                      context.push('/api-test');
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationSection({
    required LanguageProvider languageProvider,
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: AppTypography.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.currentFontFamily,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildNotificationTile({
    required LanguageProvider languageProvider,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: AppTypography.bodyLarge.copyWith(
          fontWeight: FontWeight.w500,
          fontFamily: languageProvider.currentFontFamily,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTypography.bodySmall.copyWith(
          color: AppColors.textSecondary,
          fontFamily: languageProvider.currentFontFamily,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }
}
