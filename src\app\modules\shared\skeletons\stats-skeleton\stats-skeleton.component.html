<ng-container *ngFor="let item of [].constructor(count)">
  <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md m-2 my-8">
    <div
      class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-gray-300 to-gray-200 text-white shadow-gray-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
      <div class="w-7 h-7 bg-gray-300 animate-pulse"></div>
    </div>
    <div class="p-4 text-right">
      <p class="flex justify-end antialiased font-sans text-sm leading-normal font-normal text-gray-300 animate-pulse">
        {{ 'common.loading' | transloco }}
      </p>
      <h4
        class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-gray-300 animate-pulse">
        &nbsp;
      </h4>
    </div>
    <div class="border-t border-blue-gray-50 p-4">
      <p class="flex justify-end antialiased font-sans text-base leading-relaxed font-normal text-gray-300 animate-pulse">
        &nbsp;
      </p>
    </div>
  </div>
</ng-container>
