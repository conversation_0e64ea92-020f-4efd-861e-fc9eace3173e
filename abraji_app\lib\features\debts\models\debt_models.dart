class Debt {
  final String id;
  final String userId;
  final String userName;
  final double amount;
  final String description;
  final DateTime createdAt;
  final DateTime? dueDate;
  final String status; // 'pending', 'paid', 'overdue', 'cancelled'
  final String type; // 'service', 'penalty', 'other'
  final String? reference;
  final List<DebtPayment> payments;

  Debt({
    required this.id,
    required this.userId,
    required this.userName,
    required this.amount,
    required this.description,
    required this.createdAt,
    this.dueDate,
    required this.status,
    required this.type,
    this.reference,
    this.payments = const [],
  });

  factory Debt.fromJson(Map<String, dynamic> json) {
    return Debt(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      userName: json['user_name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      description: json['description'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date']) : null,
      status: json['status'] ?? 'pending',
      type: json['type'] ?? 'service',
      reference: json['reference'],
      payments: (json['payments'] as List<dynamic>?)
          ?.map((item) => DebtPayment.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'amount': amount,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'status': status,
      'type': type,
      'reference': reference,
      'payments': payments.map((payment) => payment.toJson()).toList(),
    };
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'معلق';
      case 'paid':
        return 'مدفوع';
      case 'overdue':
        return 'متأخر';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  String get typeDisplayName {
    switch (type) {
      case 'service':
        return 'خدمة';
      case 'penalty':
        return 'غرامة';
      case 'other':
        return 'أخرى';
      default:
        return type;
    }
  }

  double get paidAmount => payments
      .where((payment) => payment.status == 'completed')
      .fold(0.0, (sum, payment) => sum + payment.amount);

  double get remainingAmount => amount - paidAmount;

  bool get isOverdue => dueDate != null && 
      DateTime.now().isAfter(dueDate!) && 
      status != 'paid' && 
      status != 'cancelled';

  bool get isPaid => status == 'paid' || remainingAmount <= 0;
}

class DebtPayment {
  final String id;
  final String debtId;
  final double amount;
  final DateTime createdAt;
  final String status; // 'pending', 'completed', 'failed'
  final String method; // 'cash', 'card', 'transfer', 'wallet'
  final String? reference;
  final String? notes;

  DebtPayment({
    required this.id,
    required this.debtId,
    required this.amount,
    required this.createdAt,
    required this.status,
    required this.method,
    this.reference,
    this.notes,
  });

  factory DebtPayment.fromJson(Map<String, dynamic> json) {
    return DebtPayment(
      id: json['id'] ?? '',
      debtId: json['debt_id'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'pending',
      method: json['method'] ?? 'cash',
      reference: json['reference'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'debt_id': debtId,
      'amount': amount,
      'created_at': createdAt.toIso8601String(),
      'status': status,
      'method': method,
      'reference': reference,
      'notes': notes,
    };
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'معلق';
      case 'completed':
        return 'مكتمل';
      case 'failed':
        return 'فاشل';
      default:
        return status;
    }
  }

  String get methodDisplayName {
    switch (method) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      case 'wallet':
        return 'محفظة';
      default:
        return method;
    }
  }
}

class CreateDebtRequest {
  final String userId;
  final double amount;
  final String description;
  final DateTime? dueDate;
  final String type;
  final String? reference;

  CreateDebtRequest({
    required this.userId,
    required this.amount,
    required this.description,
    this.dueDate,
    required this.type,
    this.reference,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'amount': amount,
      'description': description,
      'due_date': dueDate?.toIso8601String(),
      'type': type,
      'reference': reference,
    };
  }
}

class CreatePaymentRequest {
  final String debtId;
  final double amount;
  final String method;
  final String? reference;
  final String? notes;

  CreatePaymentRequest({
    required this.debtId,
    required this.amount,
    required this.method,
    this.reference,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'debt_id': debtId,
      'amount': amount,
      'method': method,
      'reference': reference,
      'notes': notes,
    };
  }
}

class DebtFilters {
  final String? userId;
  final String? status;
  final String? type;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;
  final bool? isOverdue;
  final String? search;
  final String sortBy;
  final String sortOrder;
  final int page;
  final int limit;

  DebtFilters({
    this.userId,
    this.status,
    this.type,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
    this.isOverdue,
    this.search,
    this.sortBy = 'created_at',
    this.sortOrder = 'desc',
    this.page = 1,
    this.limit = 20,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (userId != null) params['user_id'] = userId;
    if (status != null) params['status'] = status;
    if (type != null) params['type'] = type;
    if (startDate != null) params['start_date'] = startDate!.toIso8601String();
    if (endDate != null) params['end_date'] = endDate!.toIso8601String();
    if (minAmount != null) params['min_amount'] = minAmount;
    if (maxAmount != null) params['max_amount'] = maxAmount;
    if (isOverdue != null) params['is_overdue'] = isOverdue;
    if (search != null) params['search'] = search;
    
    params['sort_by'] = sortBy;
    params['sort_order'] = sortOrder;
    params['page'] = page;
    params['limit'] = limit;
    
    return params;
  }

  DebtFilters copyWith({
    String? userId,
    String? status,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
    bool? isOverdue,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? limit,
  }) {
    return DebtFilters(
      userId: userId ?? this.userId,
      status: status ?? this.status,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      isOverdue: isOverdue ?? this.isOverdue,
      search: search ?? this.search,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      page: page ?? this.page,
      limit: limit ?? this.limit,
    );
  }
}

class DebtsResponse {
  final List<Debt> debts;
  final int total;
  final int page;
  final int limit;
  final bool hasMore;
  final DebtsSummary summary;

  DebtsResponse({
    required this.debts,
    required this.total,
    required this.page,
    required this.limit,
    required this.hasMore,
    required this.summary,
  });

  factory DebtsResponse.fromJson(Map<String, dynamic> json) {
    return DebtsResponse(
      debts: (json['data'] as List<dynamic>?)
          ?.map((item) => Debt.fromJson(item))
          .toList() ?? [],
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      hasMore: json['has_more'] ?? false,
      summary: DebtsSummary.fromJson(json['summary'] ?? {}),
    );
  }
}

class DebtsSummary {
  final double totalAmount;
  final double paidAmount;
  final double pendingAmount;
  final double overdueAmount;
  final int totalCount;
  final int paidCount;
  final int pendingCount;
  final int overdueCount;

  DebtsSummary({
    required this.totalAmount,
    required this.paidAmount,
    required this.pendingAmount,
    required this.overdueAmount,
    required this.totalCount,
    required this.paidCount,
    required this.pendingCount,
    required this.overdueCount,
  });

  factory DebtsSummary.fromJson(Map<String, dynamic> json) {
    return DebtsSummary(
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      paidAmount: (json['paid_amount'] ?? 0).toDouble(),
      pendingAmount: (json['pending_amount'] ?? 0).toDouble(),
      overdueAmount: (json['overdue_amount'] ?? 0).toDouble(),
      totalCount: json['total_count'] ?? 0,
      paidCount: json['paid_count'] ?? 0,
      pendingCount: json['pending_count'] ?? 0,
      overdueCount: json['overdue_count'] ?? 0,
    );
  }
}
