import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/user_data_model.dart';

class UserOverviewTab extends StatelessWidget {
  final UserData user;

  const UserOverviewTab({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الحساب
          _buildSectionCard(
            title: 'معلومات الحساب',
            icon: Icons.account_circle,
            children: [
              _buildInfoRow('اسم المستخدم', user.username),
              _buildInfoRow('الاسم الكامل', user.fullName ?? 'غير محدد'),
              _buildInfoRow('الحالة', _getStatusText(user.status)),
              _buildInfoRow('الخطة', user.plan ?? 'غير محدد'),
              _buildInfoRow(
                'تاريخ الإنشاء',
                user.createdAt != null
                    ? _formatDate(user.createdAt!)
                    : 'غير محدد',
              ),
              if (user.expiryDate != null)
                _buildInfoRow('تاريخ الانتهاء', _formatDate(user.expiryDate!)),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات الاتصال
          _buildSectionCard(
            title: 'معلومات الاتصال',
            icon: Icons.contact_phone,
            children: [
              _buildInfoRow('البريد الإلكتروني', user.email ?? 'غير محدد'),
              _buildInfoRow('رقم الهاتف', user.phone ?? 'غير محدد'),
              _buildInfoRow('العنوان', user.address ?? 'غير محدد'),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات الشبكة
          _buildSectionCard(
            title: 'معلومات الشبكة',
            icon: Icons.network_check,
            children: [
              _buildInfoRow('عنوان IP', user.ipAddress ?? 'غير محدد'),
              _buildInfoRow('عنوان MAC', user.macAddress ?? 'غير محدد'),
              _buildInfoRow(
                'آخر تسجيل دخول',
                user.lastLogin != null
                    ? _formatDate(user.lastLogin!)
                    : 'لم يسجل دخول',
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المعلومات المالية
          _buildSectionCard(
            title: 'المعلومات المالية',
            icon: Icons.account_balance_wallet,
            children: [
              _buildInfoRow(
                'الرصيد الحالي',
                '${user.balance?.toStringAsFixed(2) ?? '0.00'} ر.س',
              ),
              _buildInfoRow(
                'إجمالي الاستهلاك',
                '0.00 ر.س',
              ), // يمكن إضافة هذا لاحقاً
              _buildInfoRow('آخر دفعة', 'غير محدد'), // يمكن إضافة هذا لاحقاً
            ],
          ),

          const SizedBox(height: 16),

          // الملاحظات
          if (user.notes != null && user.notes!.isNotEmpty)
            _buildSectionCard(
              title: 'الملاحظات',
              icon: Icons.note,
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(user.notes!, style: AppTypography.bodyMedium),
                ),
              ],
            ),

          const SizedBox(height: 16),

          // إحصائيات سريعة
          _buildQuickStats(),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppColors.primary, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.analytics,
                    color: AppColors.secondary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات سريعة',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الجلسات',
                    '12',
                    Icons.wifi,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'البيانات المستهلكة',
                    '2.5 GB',
                    Icons.data_usage,
                    AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الفواتير',
                    '8',
                    Icons.receipt,
                    AppColors.accent,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'آخر نشاط',
                    'منذ ساعة',
                    Icons.access_time,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'suspended':
        return 'معلق';
      case 'expired':
        return 'منتهي الصلاحية';
      case 'inactive':
        return 'غير نشط';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
