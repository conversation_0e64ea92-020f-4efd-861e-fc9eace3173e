import { Component, inject, OnInit } from '@angular/core';
import { UsersService } from '../../../core/user-services/services/users.service';
import {
  ColumnState,
  initialUserColumnsState,
  User,
  UserForm,
} from '../../../core/user-services/api/users';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { TableResponse } from '../../../core/common-services/interfaces/table-response';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { ToastService } from '../../shared/toast/toast.service'; // Import ToastService
import { ExcelService } from '../../../core/common-services/services/excel.service';
import { ProfileService } from '../../../core/service-profile/services/profile.service';
import { Service } from '../../../core/service-profile/api/profile';
import { Router } from '@angular/router';
import { ManagerModel } from '../../../core/user-services/api/header';
import { HeaderService } from '../../../core/user-services/services/header.service';

@Component({
  selector: 'app-all-users',
  templateUrl: './all-users.component.html',
  styleUrls: ['./all-users.component.scss'],
})
export class AllUsersComponent implements OnInit {
  selectedUsers: Array<number> = []; // Assume this is an array of selected user objects
  topSelectedUserId: number | null = null;
  userForm!: UserForm;
  quotas!: Service[];
  managers: ManagerModel[] = [];
  isLoading: boolean = false;
  exportIsLoading = false;
  userColumnsState!: ColumnState[];
  tableResponse: TableResponse<User> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };

  constructor(
    protected userService: UsersService,
    protected localStorageService: LocalStorageService,
    protected tableElementsService : TableElementsService,
    protected toastService : ToastService,
    protected excelService : ExcelService,
    protected profileService : ProfileService,
    protected headerService: HeaderService,
  ){}

  // Bind search value to form
  searchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.userForm.search = inputElement.value;
    this.userForm.page = 1;
    this.fetchUsers();
  }

  toggleSelection(userId: number): void {
    if (this.selectedUsers.indexOf(userId) === -1) {
      this.selectedUsers.push(userId);
    } else {
      const index = this.selectedUsers.indexOf(userId);
      if (index > -1) {
        this.selectedUsers.splice(index, 1);
      }
    }

    this.topSelectedUserId = this.selectedUsers.length > 0 ? this.selectedUsers[0] : null;

  }

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.userForm);
    // fetch data
    this.fetchUsers();
  }

  selectAll(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      console.log('all-checked');
      this.selectedUsers = this.tableResponse.data.map(user => Number(user.id)).filter(userId => !isNaN(userId));
      console.log(this.selectedUsers);
    } else {
      this.selectedUsers = [];
    }
    this.topSelectedUserId = this.selectedUsers.length > 0 ? this.selectedUsers[0] : null;
  }


  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(
      this.tableResponse.last_page,
      this.userForm.page
    );
  }

  // Bind the change page in pagination to the form
  changePage(page: string | number): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.userForm.page = pageNumber;
      this.fetchUsers();
    }
  }

  // Bind the change in columns visibility
  toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(
      columnKey,
      this.userColumnsState,
      this.localStorageService.UserColumnsState
    );
  }

  ngOnInit(): void {
    // initiate columns state
    this.userColumnsState =
      this.localStorageService.loadColumnsState(
        this.localStorageService.UserColumnsState
      ) || initialUserColumnsState;

    this.userForm = {
      page: 1,
      count: 10,
      sortBy: '',
      direction: '',
      search: '',
      columns: this.getVisibleColumns(),
      status: undefined,
      connection: undefined,
      profile_id: -1,
      parent_id: undefined,
      group_id: undefined,
      sub_users: undefined,
      mac: '',
    };

    const navigationData = history.state.data;
    if (navigationData) {
      console.log('Navigation Data:', navigationData);
      this.userForm = { ...this.userForm, ...navigationData };
    }

    // fetch users
    this.fetchUsers();
  }

  getVisibleColumns(): string[] {
    return this.tableElementsService.getVisibleColumns(this.userColumnsState);
  }

  fetchUsers(): void {
    console.log(this.userForm);

    this.isLoading = true;
    this.userService.getUsers(this.userForm).subscribe({
      next: (response: any) => {
        console.log("users response: ", response);

        this.tableResponse = response;
        console.log(response);
      },
      complete: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        console.error(error);
        this.toastService.addToast(
          'error',
          'Error loading users',
          'An error occurred while loading the users.'
        ); // Show error toast
      },
    });
  }

  getUserId(user: User): any {
    return this.getPropertyValue(user, 'id');
  }

  getPropertyValue(user: User, key: string): any {
    return this.tableElementsService.getUserPropertyValue(user, key);
  }

  mapPropertyName(key: string): any {
    return this.tableElementsService.mapUserPropertyName(key);
  }

  getStatusClass(status: string): string {
    const fixedStyle = ' text-xs font-medium me-2 px-2.5 py-0.5 rounded-full ';
    switch (status) {
      case 'Disabled':
        return (
          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 ' +
          fixedStyle
        );
      case 'Active':
        return (
          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' +
          fixedStyle
        );
      case 'Expired':
        return (
          'bg-orange-400 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' +
          fixedStyle
        );
      case 'Depleted':
        return (
          'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300' +
          fixedStyle
        );
      default:
        return fixedStyle;
    }
  }

  exportToExcel(): void {
    //this.exportIsLoading = true;
    // add warn
    this.toastService.addToast('warn', 'Wait Exporting Data!', 'Exporting all users data to excel sheet');
    // create request form to get all data
    const myRequestForm = {...this.userForm};
    myRequestForm.count = this.tableResponse.total;
    myRequestForm.page = 1;
    // fetch all data
    this.userService.getUsers(myRequestForm).subscribe({
      next: (response: any) => {
        this.exportIsLoading = false;
        try {
          // map the data to the format that will be exported
          const data = response.data;
          this.excelService.exportAsExcelFile(data, `allUsers`);
        } catch (error) {
          this.toastService.addToast('error', 'Error Message', 'There was an error on exporting the data to excel sheet');
          console.error('There was an error!', error);
        }
      },
      error: (error: any) => {
        this.exportIsLoading = false;
        this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the table data');
        console.error('There was an error!', error);
      },
    });

  }

  getQuotas(): void {
    if (this.quotas) return;
    this.profileService.getServices(0).subscribe({
      next: (response) => {
        this.quotas = response;
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error Fetching Profiles', error);
      }
    })
  }

  getManagers(): void
  {
    console.log("start");

    if (this.managers.length > 0) return;
    console.log("open");

    this.headerService.getAllManagers().subscribe({
      next: (response) => {
        console.log("managers: ", response);

        this.managers = response.data;
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error in get managers', error.message)
      }
    })
  }

  filterOpened(): void {
    this.getManagers();
    this.getQuotas();
  }

  // Filter change event methods
  onStatusChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.status = Number(selectElement.value);
  }

  onConnectionChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.connection = Number(selectElement.value);
  }

  onQuotaChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.profile_id = Number(selectElement.value);
  }

  onManagerChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.parent_id = Number(selectElement.value);
  }

  onGroupChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.group_id = Number(selectElement.value);
  }

  onSubUsersChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.userForm.sub_users = selectElement.value === '1'? true : selectElement.value === '0' ? false : undefined;
  }


}
