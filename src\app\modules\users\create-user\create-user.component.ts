import { Component, inject, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { UsersService } from '../../../core/user-services/services/users.service';
import { Service } from '../../../core/service-profile/api/profile';
import { ProfileService } from '../../../core/service-profile/services/profile.service';
import { UserDetails } from '../../../core/user-services/api/users';
import { AuthService } from '../../../core/auth-services/services/auth.service';
import { passwordMatchValidator } from '../validators/password-match.validator';
import { Router } from '@angular/router';
import { ToastService } from '../../shared/toast/toast.service'; // Import ToastService

@Component({
  selector: 'app-create-user',
  templateUrl: '../user-edit/user-edit.component.html',
  styleUrl: '../user-edit/user-edit.component.scss'
})
export class CreateUserComponent implements OnInit {
  private userId!: string | null;
  userDetails: UserDetails | null = null;
  isLoading = false;
  services!: Service[];
  profile_editable = true;
  username_editable = true;
  userForm!: FormGroup;

  constructor(
    private usersService: UsersService,
    private fb: FormBuilder,
    private profileService: ProfileService,
    private authService: AuthService,
    private router: Router,
    private toastService: ToastService // Inject ToastService
  ) {}

  getProfiles(userId: number): void {
    this.isLoading = true;
    this.profileService.getServices(userId).subscribe({
      next: (response) => {
        this.services = response;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.toastService.addToast('error', 'Error Fetching Profiles', 'Unable to fetch profiles. Please try again later.');
      }
    })
  }

  ngOnInit(): void {
    this.initForm();
    this.getProfiles(0);
  }

  initForm() {
    this.userForm = this.fb.group({
      username: ['', Validators.required],
      enabled: [false],
      password: [null, Validators.required],
      confirm_password: [null, Validators.required],
      profile_id: [null, Validators.required],
      parent_id: [this.authService.getAuthId()],
      use_separate_portal_password: [false],
      portal_password: [null],
      firstname: [null],
      lastname: [null],
      company: [null],
      email: [null],
      phone: [null],
      city: [null],
      address: [null],
      apartment: [null],
      street: [null],
      contract_id: [null],
      national_id: [null],
      notes: [null],
    }, {validator: passwordMatchValidator()});
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      this.isLoading = true;
      const editedUserData = this.userForm.value;
      this.usersService.createUser(editedUserData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.toastService.addToast('success', 'User Created', 'The user has been created successfully.');
          this.router.navigate(['/users/users-list']);
        },
        error: (error) => {
          this.isLoading = false;
          this.toastService.addToast('error', 'Error Creating User', 'An error occurred while creating the user. Please try again later.');
        }
      })
    } else {
      this.toastService.addToast('error', 'Form Invalid', 'Please fill out all required fields.');
    }
  }

  get username(): any {
    return this.userForm.get('username');
  }

  get password(): any {
    return this.userForm.get('password');
  }

  get confirmPassword(): any {
    return this.userForm.get('confirm_password');
  }

  get profile_id(): any {
    return this.userForm.get('profile_id');
  }
}
