<section class=" p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
  <div *ngIf="isLoading">
    <app-table-skeleton></app-table-skeleton>
  </div>
  <div *ngIf="!isLoading" class="shadow-sm">
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
      <div class="flex items-center m-4">
        <i class="pi pi-arrow-right-arrow-left me-2" style="font-size: 1.3rem"></i>
        <label for="">
          <span class="font-bold">{{ 'wallet.walletTransactions' | transloco }}</span></label>
      </div>
      <!-- data table -->
      <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" class="px-6 py-3"> # </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.transactionType' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.amount' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.created_at' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.updated_at' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.admin_id' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.debt_id' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'wallet.transaction_id' | transloco}} </th>
          </tr>
        </thead>
        <tbody>
          <tr class="bg-white border-b dark:bg-gray-900 dark:border-gray-700"
            *ngFor="let transaction of tableResponse.data; let i = index">
            <td class="px-6 py-4">{{ tableResponse.from + i }}</td>
            <td class="px-6 py-4 flex">
              @if(transaction.type === 'credit') {
              <span class="me-2">
                <svg class="w-6 h-6 text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                  height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </span>
              <span>
                {{'wallet.deposit' | transloco}}
              </span>
              }@else {
              <span class="me-2">
                <svg class="w-6 h-6 text-purple-600 dark:text-white" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </span>
              <span>
                {{'wallet.withdraw' | transloco}}
              </span>
              }
            </td>
            <td class="px-6 py-4">{{ transaction.amount }}</td>
            <td class="px-6 py-4">{{ transaction.created_at | date:'medium' }}</td>
            <td class="px-6 py-4">{{ transaction.updated_at | date:'medium' }}</td>
            <td class="px-6 py-4">{{ transaction.admin_id }}</td>
            <td class="px-6 py-4">{{ transaction.debt_id }}</td>
            <td class="px-6 py-4">{{ transaction.transaction_id }}</td>
          </tr>
        </tbody>

      </table>
      <nav
        class="flex flex-col w-full md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
        aria-label="Table navigation">
        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
          {{'table.showing' | transloco}}
          <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null? tableResponse.from
            :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
          {{'table.of' | transloco}}
          <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
        </span>
        <!-- Pagination controls -->
        <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
          <li>
            <button (click)="changePage((tableResponse.current_page - 1).toString())"
              [disabled]="tableResponse.current_page === 1"
              class="flex items-center justify-center h-full py-1.5 px-3 ms-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
              <span class="sr-only">Previous</span>
              <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </li>
          <ng-container *ngFor="let page of getPagesToDisplay()">
            <li *ngIf="page === '...'">
              <span
                class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
            </li>
            <li *ngIf="page !== '...'">
              <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
                [class.text-primary-600]="tableResponse.current_page === page"
                [class.z-10]="tableResponse.current_page === page"
                class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                {{ page }}
              </button>
            </li>
          </ng-container>
          <li>
            <button (click)="changePage((tableResponse.current_page + 1).toString())"
              [disabled]="tableResponse.current_page === tableResponse.last_page"
              class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
              <span class="sr-only">Next</span>
              <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                  clip-rule="evenodd" />
              </svg>

            </button>
          </li>
        </ul>
      </nav>
    </div>

  </div>
</section>
