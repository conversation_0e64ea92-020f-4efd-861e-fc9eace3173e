<!-- Transactions Skeleton -->
<div
  class="flex flex-col w-full max-w-md p-4 m-auto bg-white border border-gray-200 rounded-lg shadow sm:p-8 dark:bg-gray-800 dark:border-gray-700 animate-pulse">
  <div class="flex items-center justify-between mb-4">
    <div class="h-6 w-1/3 bg-gray-200 rounded dark:bg-gray-600"></div>
    <div class="h-4 w-1/4 bg-gray-200 rounded dark:bg-gray-600"></div>
  </div>
  <div class="flow-root">
    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
      <li class="py-3 sm:py-4">
        <div class="flex items-center">
          <div class="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full dark:bg-gray-600"></div>
          <div class="flex-1 min-w-0 ms-4">
            <div class="h-4 w-1/2 bg-gray-200 rounded dark:bg-gray-600"></div>
            <div class="h-3 w-1/3 mt-2 bg-gray-200 rounded dark:bg-gray-600"></div>
          </div>
          <div class="inline-flex items-center h-4 w-1/4 bg-gray-200 rounded dark:bg-gray-600"></div>
        </div>
      </li>
      <li class="py-3 sm:py-4">
        <div class="flex items-center">
          <div class="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full dark:bg-gray-600"></div>
          <div class="flex-1 min-w-0 ms-4">
            <div class="h-4 w-1/2 bg-gray-200 rounded dark:bg-gray-600"></div>
            <div class="h-3 w-1/3 mt-2 bg-gray-200 rounded dark:bg-gray-600"></div>
          </div>
          <div class="inline-flex items-center h-4 w-1/4 bg-gray-200 rounded dark:bg-gray-600"></div>
        </div>
      </li>
      <!-- Repeat the list items as needed -->
    </ul>
  </div>
  <div class="flex flex-col my-8 items-center justify-center text-gray-600 opacity-50 dark:text-gray-400">
    <div class="w-56 h-56 bg-gray-200 rounded-full dark:bg-gray-600"></div>
    <div class="h-6 w-1/2 bg-gray-200 mt-4 rounded dark:bg-gray-600"></div>
    <div class="h-4 w-1/3 mt-2 bg-gray-200 rounded dark:bg-gray-600"></div>
  </div>
</div>