import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class SasApiService {
  final String baseUrl;
  final Duration timeout;
  String? _authToken;

  SasApiService({
    required this.baseUrl,
    this.timeout = const Duration(seconds: 10),
  });

  // تعيين رمز المصادقة
  void setAuthToken(String token) {
    _authToken = token;
  }

  // الحصول على headers مع المصادقة
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'AbrajiApp/1.0.0',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  /// مصادقة المستخدم (متوافق مع AbrajiAPIs)
  Future<SasAuthResponse> authenticate({
    required String username,
    required String password,
  }) async {
    // تجربة endpoints مختلفة حسب بنية AbrajiAPIs
    final endpoints = [
      '/api/auth/login', // Primary endpoint
      '/admin/api/index.php/api/login', // SAS Radius format
      '/api/login', // Fallback
    ];

    for (final endpoint in endpoints) {
      try {
        final url = Uri.parse('$baseUrl$endpoint');

        // تحضير البيانات حسب نوع الـ endpoint
        Map<String, dynamic> requestBody;
        if (endpoint.contains('admin/api/index.php')) {
          // SAS Radius format (base64 encoded payload)
          final credentials = base64Encode(utf8.encode('$username:$password'));
          requestBody = {'payload': credentials};
        } else {
          // Standard format
          requestBody = {'username': username, 'password': password};
        }

        final response = await http
            .post(url, headers: _headers, body: json.encode(requestBody))
            .timeout(timeout);

        final data = json.decode(response.body);

        if (response.statusCode == 200) {
          // Handle different response formats
          if (data is Map<String, dynamic>) {
            // Check for success in different formats
            bool isSuccess =
                data['success'] == true ||
                data['status'] == 200 ||
                data.containsKey('token');

            if (isSuccess) {
              // Extract token and user data
              String? token = data['token'] ?? data['access_token'];
              Map<String, dynamic>? userData;

              if (data.containsKey('user')) {
                userData = data['user'];
              } else if (data.containsKey('data')) {
                userData = data['data'];
              } else {
                // Create user data from available info
                userData = {
                  'id': data['admin_id'] ?? data['id'] ?? '1',
                  'username': username,
                  'name': data['admin_name'] ?? data['name'] ?? username,
                  'email': data['email'] ?? '$<EMAIL>',
                  'role': 'admin',
                  'status': 'active',
                  'permissions': ['read', 'write'],
                };
              }

              if (token != null) {
                _authToken = token;
              }

              return SasAuthResponse(
                success: true,
                message: data['message'] ?? 'تم تسجيل الدخول بنجاح',
                user: SasUser.fromJson(userData!),
                token: token,
                expiresAt: DateTime.now().add(const Duration(hours: 24)),
              );
            }
          }
        }

        // If this endpoint failed, continue to next one
        continue;
      } catch (e) {
        // If this endpoint failed, continue to next one
        debugPrint('فشل endpoint $endpoint: $e');
        continue;
      }
    }

    // If all endpoints failed
    return SasAuthResponse(
      success: false,
      message: 'فشل في الاتصال مع جميع endpoints المتاحة',
    );
  }

  /// جلب قائمة المستخدمين (متوافق مع AbrajiAPIs)
  Future<SasUsersResponse> getUsers({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
  }) async {
    // تجربة endpoints مختلفة حسب بنية AbrajiAPIs
    final endpoints = [
      '/api/users/table', // Primary endpoint from Users module
      '/api/users/online', // Online users
      '/api/users', // Simple users
      '/api/subscribers', // Alternative naming
    ];

    for (final endpoint in endpoints) {
      try {
        final url = Uri.parse('$baseUrl$endpoint');

        // تحضير البيانات للطلب (POST format for AbrajiAPIs)
        // تحضير البيانات للطلب (تجربة تنسيقات مختلفة لتجنب 422)
        Map<String, dynamic> requestBody = {};

        // تجربة تنسيقات مختلفة حسب endpoint
        if (endpoint.contains('/table')) {
          // Format for /api/users/table (DataTables format)
          requestBody = {
            'draw': 1,
            'start': (page - 1) * limit,
            'length': limit,
          };
          if (search != null && search.isNotEmpty) {
            requestBody['search'] = {'value': search};
          }
        } else if (endpoint.contains('/online')) {
          // Format for /api/users/online - usually no body needed
          requestBody = {};
        } else {
          // Standard format
          requestBody = {'page': page, 'limit': limit};
          if (search != null && search.isNotEmpty) {
            requestBody['search'] = search;
          }
          if (status != null && status.isNotEmpty) {
            requestBody['status'] = status;
          }
        }

        // تجربة GET أولاً، ثم POST
        http.Response response;
        try {
          // تجربة GET مع query parameters
          if (endpoint.contains('/online') || requestBody.isEmpty) {
            response = await http.get(url, headers: _headers).timeout(timeout);
          } else {
            response = await http
                .post(url, headers: _headers, body: json.encode(requestBody))
                .timeout(timeout);
          }
        } catch (e) {
          // إذا فشل، جرب الطريقة الأخرى
          if (endpoint.contains('/online')) {
            response = await http
                .post(url, headers: _headers, body: json.encode({}))
                .timeout(timeout);
          } else {
            response = await http.get(url, headers: _headers).timeout(timeout);
          }
        }

        final data = json.decode(response.body);

        if (response.statusCode == 200) {
          // Handle different response formats from AbrajiAPIs
          List<dynamic> usersData = [];

          if (data is Map<String, dynamic>) {
            if (data.containsKey('data') && data['data'] is List) {
              usersData = data['data'] as List<dynamic>;
            } else if (data.containsKey('users') && data['users'] is List) {
              usersData = data['users'] as List<dynamic>;
            }
          } else if (data is List) {
            usersData = data;
          }

          final users = usersData.map((userJson) {
            // Ensure userJson is a Map
            if (userJson is Map<String, dynamic>) {
              return SasUser.fromJson(userJson);
            } else {
              // Create a basic user structure if data format is different
              return SasUser(
                id: userJson['id']?.toString() ?? '0',
                username: userJson['username'] ?? 'unknown',
                name:
                    userJson['name'] ?? userJson['username'] ?? 'Unknown User',
                email: userJson['email'] ?? '<EMAIL>',
                role: userJson['role'] ?? 'user',
                status: userJson['status'] ?? 'active',
                permissions: ['read'],
              );
            }
          }).toList();

          // Create pagination info
          SasPagination? pagination;
          if (data is Map<String, dynamic> && data.containsKey('pagination')) {
            pagination = SasPagination.fromJson(data['pagination']);
          } else {
            pagination = SasPagination(
              currentPage: page,
              totalPages: 1,
              totalItems: users.length,
              perPage: limit,
            );
          }

          return SasUsersResponse(
            success: true,
            users: users,
            pagination: pagination,
          );
        }

        // If this endpoint failed, continue to next one
        continue;
      } catch (e) {
        // If this endpoint failed, continue to next one
        debugPrint('فشل endpoint $endpoint: $e');
        continue;
      }
    }

    // If all endpoints failed
    return SasUsersResponse(
      success: false,
      message: 'فشل في الاتصال مع جميع endpoints المتاحة',
      users: [],
    );
  }

  /// اختبار الاتصال
  Future<bool> testConnection() async {
    try {
      final url = Uri.parse('$baseUrl/api/sas/test-connection');

      final response = await http.post(url, headers: _headers).timeout(timeout);

      final data = json.decode(response.body);
      return response.statusCode == 200 && data['success'] == true;
    } catch (e) {
      debugPrint('خطأ في اختبار الاتصال: $e');
      return false;
    }
  }

  /// جلب بيانات مستخدم محدد
  Future<SasUser?> getUser(String userId) async {
    try {
      final url = Uri.parse('$baseUrl/api/sas/user/$userId');

      final response = await http.get(url, headers: _headers).timeout(timeout);

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return SasUser.fromJson(data['data']);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }
}

/// نموذج رد المصادقة
class SasAuthResponse {
  final bool success;
  final String message;
  final SasUser? user;
  final String? token;
  final DateTime? expiresAt;

  SasAuthResponse({
    required this.success,
    required this.message,
    this.user,
    this.token,
    this.expiresAt,
  });
}

/// نموذج رد المستخدمين
class SasUsersResponse {
  final bool success;
  final String? message;
  final List<SasUser> users;
  final SasPagination? pagination;

  SasUsersResponse({
    required this.success,
    this.message,
    required this.users,
    this.pagination,
  });
}

/// نموذج المستخدم
class SasUser {
  final String id;
  final String username;
  final String name;
  final String email;
  final String role;
  final String status;
  final DateTime? lastLogin;
  final List<String> permissions;

  SasUser({
    required this.id,
    required this.username,
    required this.name,
    required this.email,
    required this.role,
    required this.status,
    this.lastLogin,
    required this.permissions,
  });

  factory SasUser.fromJson(Map<String, dynamic> json) {
    return SasUser(
      id: json['id'].toString(),
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? 'user',
      status: json['status'] ?? 'active',
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'])
          : null,
      permissions: List<String>.from(json['permissions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'email': email,
      'role': role,
      'status': status,
      'last_login': lastLogin?.toIso8601String(),
      'permissions': permissions,
    };
  }
}

/// نموذج الترقيم
class SasPagination {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int perPage;

  SasPagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.perPage,
  });

  factory SasPagination.fromJson(Map<String, dynamic> json) {
    return SasPagination(
      currentPage: json['current_page'] ?? 1,
      totalPages: json['total_pages'] ?? 1,
      totalItems: json['total_items'] ?? 0,
      perPage: json['per_page'] ?? 20,
    );
  }

  bool get hasNextPage => currentPage < totalPages;
  bool get hasPreviousPage => currentPage > 1;
}
