import { Injectable } from '@angular/core';
import { DebtsApi } from '../api/debts.api';
import { IDebtsService } from './IDebtsService';
import { Observable } from 'rxjs';
import { Debt } from '../api/debts';
import { TableResponse } from '../../common-services/interfaces/table-response';

@Injectable({
  providedIn: 'root'
})
export class DebtsService implements IDebtsService {
  constructor(
    private debtsApi: DebtsApi,
  ) {}

  getDebtHistory(debtId: string): Observable<any> {
    return this.debtsApi.getDebtHistory(debtId);
  }

  getDebtStatisticsByUser(id: number): Observable<any> {
    return this.debtsApi.getDebtStatisticsByUser(id);
  }

  getDebtsByUser(id: number, requestForm: any): Observable<TableResponse<Debt>> {
    return this.debtsApi.getDebtsByUser(id, requestForm);
  }

  getDebts(requestForm: any) {
    return this.debtsApi.getDebts(requestForm);
  }

  getDebt(): Observable<any> {
    return this.debtsApi.getDebt();
  }

  createDebt(debt: Debt): Observable<any> {
    return this.debtsApi.createDebt(debt);
  }

  updateDebt(debt: any): Observable<any> {
    return this.debtsApi.updateDebt(debt);
  }

  deleteDebt(id: number): Observable<any> {
    return this.debtsApi.deleteDebt(id);
  }

  payDebt(id: number): Observable<any> {
    return this.debtsApi.payDebt(id);
  }

  payPartialDebt(id: number, amount: number): Observable<any> {
    return this.debtsApi.payPartialDebt(id, amount);
  }
}

