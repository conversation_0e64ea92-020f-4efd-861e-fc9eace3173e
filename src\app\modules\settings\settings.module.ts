import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SettingsRoutingModule } from './settings-routing.module';
import { TowersComponent } from './towers/towers.component';
import { PrinterSettingsComponent } from './printer-settings/printer-settings.component';
import { TranslocoModule } from '@jsverse/transloco';

@NgModule({
  declarations: [TowersComponent, PrinterSettingsComponent],
  imports: [CommonModule, SettingsRoutingModule,TranslocoModule],
})
export class SettingsModule {}
