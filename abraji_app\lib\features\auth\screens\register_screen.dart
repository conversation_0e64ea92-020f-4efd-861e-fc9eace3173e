import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../providers/auth_provider.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  late AnimationController _animationController;
  bool _isLoading = false;
  bool _acceptTerms = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_acceptTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى الموافقة على الشروط والأحكام'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.register(
      name: _nameController.text,
      email: _emailController.text,
      password: _passwordController.text,
      confirmPassword: _confirmPasswordController.text,
      phone: _phoneController.text.isNotEmpty ? _phoneController.text : null,
    );

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      context.go('/dashboard');
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.error ?? 'خطأ في إنشاء الحساب'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.secondary,
              AppColors.secondaryLight,
              AppColors.accent,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),

                // Header
                _buildHeader(),

                const SizedBox(height: 32),

                // Register Form Card
                _buildRegisterForm(),

                const SizedBox(height: 24),

                // Login Link
                _buildLoginLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Back Button
        Align(
              alignment: Alignment.centerRight,
              child: IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
            )
            .animate(controller: _animationController)
            .fadeIn(duration: 400.ms)
            .slideX(begin: 30, end: 0, duration: 400.ms),

        const SizedBox(height: 16),

        // Title
        Text(
              'إنشاء حساب جديد',
              style: AppTypography.headlineLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            )
            .animate(controller: _animationController)
            .fadeIn(duration: 600.ms, delay: 200.ms)
            .slideY(begin: 30, end: 0, duration: 600.ms, delay: 200.ms),

        const SizedBox(height: 8),

        Text(
              'أدخل بياناتك لإنشاء حساب جديد',
              style: AppTypography.bodyLarge.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            )
            .animate(controller: _animationController)
            .fadeIn(duration: 600.ms, delay: 400.ms)
            .slideY(begin: 20, end: 0, duration: 600.ms, delay: 400.ms),
      ],
    );
  }

  Widget _buildRegisterForm() {
    return Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Name Field
                CustomTextField(
                      controller: _nameController,
                      label: 'الاسم الكامل',
                      hint: 'أدخل اسمك الكامل',
                      prefixIcon: Icons.person_outlined,
                      textInputAction: TextInputAction.next,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الاسم الكامل';
                        }
                        if (value.length < 2) {
                          return 'الاسم يجب أن يكون حرفين على الأقل';
                        }
                        return null;
                      },
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 600.ms)
                    .slideX(
                      begin: -50,
                      end: 0,
                      duration: 600.ms,
                      delay: 600.ms,
                    ),

                const SizedBox(height: 16),

                // Email Field
                EmailTextField(controller: _emailController)
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 700.ms)
                    .slideX(
                      begin: -50,
                      end: 0,
                      duration: 600.ms,
                      delay: 700.ms,
                    ),

                const SizedBox(height: 16),

                // Phone Field
                CustomTextField(
                      controller: _phoneController,
                      label: 'رقم الهاتف (اختياري)',
                      hint: 'أدخل رقم هاتفك',
                      prefixIcon: Icons.phone_outlined,
                      keyboardType: TextInputType.phone,
                      textInputAction: TextInputAction.next,
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 800.ms)
                    .slideX(
                      begin: -50,
                      end: 0,
                      duration: 600.ms,
                      delay: 800.ms,
                    ),

                const SizedBox(height: 16),

                // Password Field
                PasswordTextField(
                      controller: _passwordController,
                      label: 'كلمة المرور',
                      hint: 'أدخل كلمة المرور',
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 900.ms)
                    .slideX(
                      begin: -50,
                      end: 0,
                      duration: 600.ms,
                      delay: 900.ms,
                    ),

                const SizedBox(height: 16),

                // Confirm Password Field
                PasswordTextField(
                      controller: _confirmPasswordController,
                      label: 'تأكيد كلمة المرور',
                      hint: 'أعد إدخال كلمة المرور',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى تأكيد كلمة المرور';
                        }
                        if (value != _passwordController.text) {
                          return 'كلمات المرور غير متطابقة';
                        }
                        return null;
                      },
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 1000.ms)
                    .slideX(
                      begin: -50,
                      end: 0,
                      duration: 600.ms,
                      delay: 1000.ms,
                    ),

                const SizedBox(height: 20),

                // Terms and Conditions
                Row(
                      children: [
                        Checkbox(
                          value: _acceptTerms,
                          onChanged: (value) {
                            setState(() {
                              _acceptTerms = value ?? false;
                            });
                          },
                          activeColor: AppColors.primary,
                        ),
                        Expanded(
                          child: Text.rich(
                            TextSpan(
                              text: 'أوافق على ',
                              style: AppTypography.bodySmall,
                              children: [
                                TextSpan(
                                  text: 'الشروط والأحكام',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const TextSpan(text: ' و '),
                                TextSpan(
                                  text: 'سياسة الخصوصية',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 1100.ms),

                const SizedBox(height: 24),

                // Register Button
                Container(
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: AppColors.secondaryGradient,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.secondary.withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _isLoading ? null : _register,
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: _isLoading
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    'إنشاء الحساب',
                                    style: AppTypography.buttonLarge.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    )
                    .animate(controller: _animationController)
                    .fadeIn(duration: 600.ms, delay: 1200.ms)
                    .slideY(
                      begin: 20,
                      end: 0,
                      duration: 600.ms,
                      delay: 1200.ms,
                    ),
              ],
            ),
          ),
        )
        .animate(controller: _animationController)
        .fadeIn(duration: 800.ms, delay: 500.ms)
        .slideY(begin: 50, end: 0, duration: 800.ms, delay: 500.ms);
  }

  Widget _buildLoginLink() {
    return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'لديك حساب بالفعل؟ ',
              style: AppTypography.bodyMedium.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            TextButton(
              onPressed: () => context.pop(),
              child: Text(
                'تسجيل الدخول',
                style: AppTypography.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        )
        .animate(controller: _animationController)
        .fadeIn(duration: 600.ms, delay: 1300.ms);
  }
}
