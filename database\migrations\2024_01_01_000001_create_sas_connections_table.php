<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sas_connections', function (Blueprint $table) {
            $table->id();
            $table->string('connection_name')->default('Default SAS Connection');
            $table->string('server_url');
            $table->string('server_ip')->nullable();
            $table->integer('server_port')->default(1812);
            $table->string('username');
            $table->string('password'); // سيتم تشفيرها
            $table->string('shared_secret')->default('testing123');
            $table->enum('connection_type', ['radius', 'http', 'https'])->default('http');
            $table->enum('status', ['active', 'inactive', 'testing'])->default('testing');
            $table->json('connection_settings')->nullable(); // إعدادات إضافية
            $table->timestamp('last_tested_at')->nullable();
            $table->timestamp('last_successful_at')->nullable();
            $table->text('last_error')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'created_at']);
            $table->index('server_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sas_connections');
    }
};
