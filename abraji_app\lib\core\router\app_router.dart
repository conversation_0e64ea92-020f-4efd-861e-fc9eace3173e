import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/onboarding/screens/splash_screen.dart';
import '../../features/home/<USER>/main_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/transactions/screens/transactions_screen.dart';
import '../../features/transactions/screens/transaction_detail_screen.dart';
import '../../features/wallet/screens/wallet_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/users/screens/users_screen.dart';
import '../../features/users/screens/add_user_screen.dart';
import '../../features/users/screens/user_details_screen.dart';
import '../../features/users/screens/user_qr_screen.dart';
import '../../features/users/models/user_data_model.dart';
import '../../features/debts/screens/debts_screen.dart';
import '../../features/settings/screens/simple_settings_screen.dart';
import '../../features/debug/screens/api_test_screen.dart';
import '../../features/debug/screens/optimized_test_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/dashboard',
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Onboarding
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),

      // SAS Connection Route
      GoRoute(
        path: '/sas-connection',
        name: 'sas-connection',
        builder: (context, state) => const SasConnectionScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Main App Routes
      ShellRoute(
        builder: (context, state, child) => MainScreen(child: child),
        routes: [
          // Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),

          // Transactions
          GoRoute(
            path: '/transactions',
            name: 'transactions',
            builder: (context, state) => const TransactionsScreen(),
            routes: [
              GoRoute(
                path: '/detail/:id',
                name: 'transaction-detail',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return TransactionDetailScreen(transactionId: id);
                },
              ),
            ],
          ),

          // Wallet
          GoRoute(
            path: '/wallet',
            name: 'wallet',
            builder: (context, state) => const WalletScreen(),
          ),

          // Profile
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),

          // Debts
          GoRoute(
            path: '/debts',
            name: 'debts',
            builder: (context, state) => const DebtsScreen(),
          ),

          // Settings
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SimpleSettingsScreen(),
          ),

          // API Test (Debug)
          GoRoute(
            path: '/api-test',
            name: 'api-test',
            builder: (context, state) => const ApiTestScreen(),
          ),

          // Optimized Test (Debug)
          GoRoute(
            path: '/optimized-test',
            name: 'optimized-test',
            builder: (context, state) => const OptimizedTestScreen(),
          ),

          // Users
          GoRoute(
            path: '/users',
            name: 'users',
            builder: (context, state) => const UsersScreen(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-user',
                builder: (context, state) => const AddUserScreen(),
              ),
              GoRoute(
                path: '/:userId',
                name: 'user-details',
                builder: (context, state) {
                  final userId = state.pathParameters['userId']!;
                  return UserDetailsScreen(userId: userId);
                },
                routes: [
                  GoRoute(
                    path: '/qr',
                    name: 'user-qr',
                    builder: (context, state) {
                      final userId = state.pathParameters['userId']!;
                      // هنا يجب الحصول على بيانات المستخدم
                      // سنستخدم بيانات وهمية للآن
                      final user = UserData(
                        id: userId,
                        username: 'user_$userId',
                        status: 'active',
                        createdAt: DateTime.now(),
                      );
                      return UserQRScreen(user: user);
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'صفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار المطلوب غير متاح',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
}

// Route Names for easy access
class AppRoutes {
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String dashboard = '/dashboard';
  static const String transactions = '/transactions';
  static const String transactionDetail = '/transactions/detail';
  static const String wallet = '/wallet';
  static const String profile = '/profile';
  static const String settings = '/settings';
}

// Navigation Helper
class AppNavigation {
  static void goToSplash(BuildContext context) {
    context.go(AppRoutes.splash);
  }

  static void goToOnboarding(BuildContext context) {
    context.go(AppRoutes.onboarding);
  }

  static void goToLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }

  static void goToRegister(BuildContext context) {
    context.go(AppRoutes.register);
  }

  static void goToForgotPassword(BuildContext context) {
    context.go(AppRoutes.forgotPassword);
  }

  static void goToDashboard(BuildContext context) {
    context.go(AppRoutes.dashboard);
  }

  static void goToTransactions(BuildContext context) {
    context.go(AppRoutes.transactions);
  }

  static void goToTransactionDetail(BuildContext context, String id) {
    context.go('${AppRoutes.transactions}/detail/$id');
  }

  static void goToWallet(BuildContext context) {
    context.go(AppRoutes.wallet);
  }

  static void goToProfile(BuildContext context) {
    context.go(AppRoutes.profile);
  }

  static void goToSettings(BuildContext context) {
    context.go(AppRoutes.settings);
  }

  static void pushToLogin(BuildContext context) {
    context.push(AppRoutes.login);
  }

  static void pushToRegister(BuildContext context) {
    context.push(AppRoutes.register);
  }

  static void pushToTransactionDetail(BuildContext context, String id) {
    context.push('${AppRoutes.transactions}/detail/$id');
  }

  static void pop(BuildContext context) {
    context.pop();
  }

  static bool canPop(BuildContext context) {
    return context.canPop();
  }
}
