@media print {
  @page {
    size: 88mm auto;
    margin: 0; // Optional: Remove default margins
  }

  body {
    width: 88mm;
    margin: 0; // Optional: Remove default margins
    font-family: Arial, sans-serif;
  }

  .max-w-4xl {
    max-width: 100%;
    padding: 0.5rem;
  }

  .border {
    border-width: 1px;
  }

  .border-gray-300 {
    border-color: #d1d5db;
  }

  .border-gray-950 {
    border-color: #111827;
  }

  .text-lg {
    font-size: 1.125rem;
  }

  .font-bold {
    font-weight: bold;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .p-8 {
    padding: 2rem;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .mb-8 {
    margin-bottom: 2rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .mt-8 {
    margin-top: 2rem;
  }

  .w-full {
    width: 100%;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  .flex-col {
    flex-direction: column;
  }

  .justify-center {
    justify-content: center;
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
}
