import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/wallet_models.dart';
import '../widgets/wallet_balance_card.dart';
import '../widgets/transaction_card.dart';
import '../widgets/quick_actions_card.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  WalletBalance? walletBalance;
  List<WalletTransaction> recentTransactions = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWalletData();
  }

  Future<void> _loadWalletData() async {
    if (!mounted) return;

    setState(() {
      isLoading = true;
    });

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      setState(() {
        walletBalance = _getMockWalletBalance();
        recentTransactions = _getMockTransactions();
        isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        isLoading = false;
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
    }
  }

  WalletBalance _getMockWalletBalance() {
    return WalletBalance(
      userId: 'user_1',
      currentBalance: 2500.75,
      totalDeposits: 5000.0,
      totalWithdrawals: 1200.0,
      totalPayments: 1300.25,
      totalRefunds: 0.0,
      lastUpdated: DateTime.now().subtract(const Duration(minutes: 5)),
    );
  }

  List<WalletTransaction> _getMockTransactions() {
    return [
      WalletTransaction(
        id: 'txn_1',
        userId: 'user_1',
        type: 'deposit',
        amount: 500.0,
        description: 'إيداع نقدي',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        status: 'completed',
        reference: 'DEP001',
      ),
      WalletTransaction(
        id: 'txn_2',
        userId: 'user_1',
        type: 'payment',
        amount: 100.0,
        description: 'دفع فاتورة اشتراك شهري',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        status: 'completed',
        reference: 'PAY001',
      ),
      WalletTransaction(
        id: 'txn_3',
        userId: 'user_1',
        type: 'withdrawal',
        amount: 200.0,
        description: 'سحب نقدي',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        status: 'pending',
        reference: 'WTH001',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المحفظة'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWalletData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadWalletData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بطاقة الرصيد
                    if (walletBalance != null)
                      WalletBalanceCard(
                        balance: walletBalance!,
                        onRefresh: _loadWalletData,
                      ),

                    const SizedBox(height: 20),

                    // الإجراءات السريعة
                    QuickActionsCard(
                      onDeposit: _showDepositDialog,
                      onWithdraw: _showWithdrawDialog,
                      onTransfer: _showTransferDialog,
                      onHistory: () => context.push('/wallet/history'),
                    ),

                    const SizedBox(height: 20),

                    // المعاملات الأخيرة
                    Row(
                      children: [
                        Text(
                          'المعاملات الأخيرة',
                          style: AppTypography.titleLarge.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () => context.push('/wallet/history'),
                          child: const Text('عرض الكل'),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    if (recentTransactions.isEmpty)
                      _buildEmptyTransactions()
                    else
                      ...recentTransactions.map(
                        (transaction) => TransactionCard(
                          transaction: transaction,
                          onTap: () => _showTransactionDetails(transaction),
                        ),
                      ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildEmptyTransactions() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد معاملات',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم تقم بأي معاملات حتى الآن',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _showDepositDialog() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم فتح نافذة الإيداع')));
  }

  void _showWithdrawDialog() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم فتح نافذة السحب')));
  }

  void _showTransferDialog() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم فتح نافذة التحويل')));
  }

  void _showTransactionDetails(WalletTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المعاملة ${transaction.id}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${transaction.typeDisplayName}'),
            Text('المبلغ: ${transaction.amount.toStringAsFixed(2)} ر.س'),
            Text('الوصف: ${transaction.description}'),
            Text('الحالة: ${transaction.statusDisplayName}'),
            if (transaction.reference != null)
              Text('المرجع: ${transaction.reference}'),
            Text('التاريخ: ${transaction.createdAt}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
