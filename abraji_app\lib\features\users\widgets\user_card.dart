import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/user_data_model.dart';

class UserCard extends StatelessWidget {
  final UserData user;
  final VoidCallback? onTap;
  final Function(String)? onStatusChanged;
  final VoidCallback? onDisconnect;

  const UserCard({
    super.key,
    required this.user,
    this.onTap,
    this.onStatusChanged,
    this.onDisconnect,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الاسم والحالة
              Row(
                children: [
                  // أيقونة المستخدم
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: _getStatusColor().withValues(alpha: 0.1),
                    child: Icon(
                      Icons.person,
                      color: _getStatusColor(),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.displayName,
                          style: AppTypography.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '@${user.username}',
                          style: AppTypography.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // شارة الحالة
                  _buildStatusBadge(),
                ],
              ),

              const SizedBox(height: 16),

              // الصف الثاني: معلومات إضافية
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      Icons.email_outlined,
                      user.email ?? 'غير محدد',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      Icons.phone_outlined,
                      user.phone ?? 'غير محدد',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثالث: معلومات تقنية
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      Icons.computer_outlined,
                      user.ipAddress ?? 'غير محدد',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      Icons.account_balance_wallet_outlined,
                      user.balance != null ? '${user.balance} ر.س' : 'غير محدد',
                    ),
                  ),
                ],
              ),

              if (user.plan != null || user.expiryDate != null) ...[
                const SizedBox(height: 12),

                // الصف الرابع: الخطة وتاريخ الانتهاء
                Row(
                  children: [
                    if (user.plan != null)
                      Expanded(
                        child: _buildInfoItem(
                          Icons.card_membership_outlined,
                          user.plan!,
                        ),
                      ),
                    if (user.plan != null && user.expiryDate != null)
                      const SizedBox(width: 16),
                    if (user.expiryDate != null)
                      Expanded(
                        child: _buildInfoItem(
                          Icons.schedule_outlined,
                          _formatDate(user.expiryDate!),
                          color: user.isExpired ? AppColors.error : null,
                        ),
                      ),
                  ],
                ),
              ],

              // أزرار الإجراءات
              if (onStatusChanged != null || onDisconnect != null) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),

                Row(
                  children: [
                    if (onStatusChanged != null) ...[
                      Expanded(
                        child: _buildActionButton(
                          context,
                          user.isActive ? 'تعليق' : 'تفعيل',
                          user.isActive ? Icons.pause : Icons.play_arrow,
                          user.isActive ? AppColors.warning : AppColors.success,
                          () => onStatusChanged!(
                            user.isActive ? 'suspended' : 'active',
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],

                    if (onDisconnect != null && user.isActive)
                      Expanded(
                        child: _buildActionButton(
                          context,
                          'قطع الاتصال',
                          Icons.logout,
                          AppColors.error,
                          onDisconnect!,
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
      ),
      child: Text(
        user.statusDisplayName,
        style: AppTypography.bodySmall.copyWith(
          color: _getStatusColor(),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, {Color? color}) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color ?? AppColors.textSecondary),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            text,
            style: AppTypography.bodySmall.copyWith(
              color: color ?? AppColors.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Color _getStatusColor() {
    switch (user.status.toLowerCase()) {
      case 'active':
        return AppColors.success;
      case 'suspended':
        return AppColors.warning;
      case 'expired':
        return AppColors.error;
      case 'inactive':
        return AppColors.textSecondary;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference < 0) {
      return 'منتهي منذ ${(-difference)} يوم';
    } else if (difference == 0) {
      return 'ينتهي اليوم';
    } else if (difference == 1) {
      return 'ينتهي غداً';
    } else if (difference <= 7) {
      return 'ينتهي خلال $difference أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
