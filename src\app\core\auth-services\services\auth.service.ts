import { inject, Injectable } from '@angular/core';
import { AuthData, loginDTO } from '../api/auth';
import { Observable, tap } from 'rxjs';
import { AuthApi } from '../api/auth.api';
import { CacheService } from '../../common-services/services/cache.service';
import { LocalStorageService } from './local-storage.service';
import { ManagerModel } from '../../user-services/api/header';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService implements AuthData {
  private router = inject(Router);
  private authApi = inject(AuthApi);
  private cacheService = inject(CacheService);
  private localStorageService = inject(LocalStorageService);
  public redirectTo : string | null = null;

  login(loginDTO: loginDTO): Observable<string> {
    this.cacheService.clearAllCaches();
    return this.authApi.login(loginDTO);
  }

  isAuthenticated(): boolean {
    return this.authApi.isAuthenticated();
  }

  getAuthId(): string | null {
    return this.authApi.getAuthId();
  }

  logout(): void {
    this.removeStoredUserData();
    localStorage.clear();
    this.cacheService.clearAllCaches();
    console.log("storage", localStorage);
  }

  private removeStoredUserData(): void {
    this.localStorageService.removeToken();
    this.localStorageService.removeByKey(this.localStorageService.CREDENTIALS);
  }

  pushUserAccount(manager: ManagerModel): void {
    let accounts = this.localStorageService.getObj(this.localStorageService.UserAccounts);
    if (accounts && accounts.length > 0){
      // just add account to the new accounts
      accounts = [...accounts, manager].filter((value, index, self) =>
        index === self.findIndex((t) => (t.username === value.username && t.email === value.email)));
    }
    else
      accounts = [manager];
    // save
    this.localStorageService.saveObj(accounts, this.localStorageService.UserAccounts);
    const accountsFromStorage = this.localStorageService.getObj(this.localStorageService.UserAccounts);
    console.log("accounts", accountsFromStorage);
  }

  getAccounts(): ManagerModel[] {
    return this.localStorageService.getObj(this.localStorageService.UserAccounts);
  }

  performLogin(credentials: loginDTO): Observable<any> {
    return this.login(credentials).pipe(
      tap({
        next: (response) => {
          this.updateUserAccounts(credentials.username, credentials.password, Number(this.getAuthId()));
        },
        error: (error) => {
        },
      })
    );
  }


  private updateUserAccounts(username: string, password: string, id: number): void {
    const manager: ManagerModel = {
      username: username,
      email: password,
      id: id,
    }
    this.pushUserAccount(manager);
    console.log("localStorage", this.localStorageService.getObj(this.localStorageService.UserAccounts));

  }

}
