import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/providers/language_provider.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../providers/settings_provider.dart';
import '../models/setting_model.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Controllers for SAS Radius settings
  final _sasUrlController = TextEditingController();
  final _sasIpController = TextEditingController();
  final _sasPortController = TextEditingController();
  final _sasSecretController = TextEditingController();

  // Controllers for general settings
  final _appNameController = TextEditingController();
  final _appVersionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // إضافة تبويب اللغة
    // تأخير تحميل الإعدادات حتى يكون context جاهز
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSettings();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _sasUrlController.dispose();
    _sasIpController.dispose();
    _sasPortController.dispose();
    _sasSecretController.dispose();
    _appNameController.dispose();
    _appVersionController.dispose();
    super.dispose();
  }

  void _loadSettings() {
    try {
      if (!mounted) return;

      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      // تحميل إعدادات SAS Radius
      _sasUrlController.text =
          settingsProvider.getSettingValue(SettingKeys.sasRadiusUrl) ?? '';
      _sasIpController.text =
          settingsProvider.getSettingValue(SettingKeys.sasRadiusIp) ?? '';
      _sasPortController.text =
          settingsProvider.getSettingValue(SettingKeys.sasRadiusPort) ?? '';
      _sasSecretController.text =
          settingsProvider.getSettingValue(SettingKeys.sasRadiusSecret) ?? '';

      // تحميل الإعدادات العامة
      _appNameController.text =
          settingsProvider.getSettingValue(SettingKeys.appName) ?? 'AbrajiAPIs';
      _appVersionController.text =
          settingsProvider.getSettingValue(SettingKeys.appVersion) ?? '1.0.0';
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
      // تعيين قيم افتراضية في حالة الخطأ
      _appNameController.text = 'AbrajiAPIs';
      _appVersionController.text = '1.0.0';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: AppTypography.titleLarge.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: 'SAS Radius'),
            Tab(text: 'عام'),
            Tab(text: 'اللغة'),
            Tab(text: 'الإشعارات'),
          ],
        ),
        actions: [
          Consumer<SettingsProvider>(
            builder: (context, settingsProvider, child) {
              return IconButton(
                onPressed: settingsProvider.isLoading
                    ? null
                    : () {
                        settingsProvider.refresh();
                      },
                icon: settingsProvider.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
              );
            },
          ),
        ],
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل الإعدادات',
                    style: AppTypography.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    settingsProvider.error!,
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      settingsProvider.clearError();
                      settingsProvider.refresh();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildSasRadiusTab(settingsProvider),
              _buildGeneralTab(settingsProvider),
              _buildLanguageTab(),
              _buildNotificationsTab(settingsProvider),
            ],
          );
        },
      ),
    );
  }

  // تبويب إعدادات SAS Radius
  Widget _buildSasRadiusTab(SettingsProvider settingsProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات SAS Radius',
            style: AppTypography.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإدخال IP أو رابط SAS Radius الخاص بك',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),

          // SAS Radius URL
          CustomTextField(
            controller: _sasUrlController,
            label: 'رابط SAS Radius',
            hint: 'https://example.com/sas-radius',
            prefixIcon: Icons.link,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          // SAS Radius IP
          CustomTextField(
            controller: _sasIpController,
            label: 'IP Address',
            hint: '*************',
            prefixIcon: Icons.computer,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),

          // SAS Radius Port
          CustomTextField(
            controller: _sasPortController,
            label: 'Port',
            hint: '1812',
            prefixIcon: Icons.settings_ethernet,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),

          // SAS Radius Secret
          CustomTextField(
            controller: _sasSecretController,
            label: 'Secret Key',
            hint: 'أدخل المفتاح السري',
            prefixIcon: Icons.key,
            obscureText: true,
          ),
          const SizedBox(height: 24),

          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: settingsProvider.isLoading
                      ? null
                      : _saveSasRadiusSettings,
                  icon: const Icon(Icons.save),
                  label: const Text('حفظ الإعدادات'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: settingsProvider.isLoading
                      ? null
                      : _testSasRadiusConnection,
                  icon: const Icon(Icons.wifi_tethering),
                  label: const Text('اختبار الاتصال'),
                ),
              ),
            ],
          ),

          if (settingsProvider.sasRadiusUrl != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: AppColors.success),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'URL الحالي: ${settingsProvider.sasRadiusUrl}',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // تبويب الإعدادات العامة
  Widget _buildGeneralTab(SettingsProvider settingsProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإعدادات العامة',
            style: AppTypography.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // اسم التطبيق
          CustomTextField(
            controller: _appNameController,
            label: 'اسم التطبيق',
            hint: 'AbrajiAPIs',
            prefixIcon: Icons.apps,
          ),
          const SizedBox(height: 16),

          // إصدار التطبيق
          CustomTextField(
            controller: _appVersionController,
            label: 'إصدار التطبيق',
            hint: '1.0.0',
            prefixIcon: Icons.info,
          ),
          const SizedBox(height: 24),

          // زر الحفظ
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: settingsProvider.isLoading
                  ? null
                  : _saveGeneralSettings,
              icon: const Icon(Icons.save),
              label: const Text('حفظ الإعدادات'),
            ),
          ),
        ],
      ),
    );
  }

  // تبويب إعدادات اللغة
  Widget _buildLanguageTab() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.isArabic ? 'اللغة' : 'Language',
                style: AppTypography.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                languageProvider.isArabic
                    ? 'اختر لغة التطبيق المفضلة لديك'
                    : 'Choose your preferred app language',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontFamily: languageProvider.currentFontFamily,
                ),
              ),
              const SizedBox(height: 24),

              // خيارات اللغة
              _buildLanguageOption(
                languageProvider: languageProvider,
                title: languageProvider.isArabic ? 'العربية' : 'Arabic',
                subtitle: 'العربية - السعودية',
                isSelected: languageProvider.isArabic,
                onTap: () => languageProvider.setArabic(),
                fontFamily: LanguageProvider.arabicFont,
              ),

              const SizedBox(height: 12),

              _buildLanguageOption(
                languageProvider: languageProvider,
                title: languageProvider.isArabic ? 'الإنجليزية' : 'English',
                subtitle: 'English - United States',
                isSelected: languageProvider.isEnglish,
                onTap: () => languageProvider.setEnglish(),
                fontFamily: LanguageProvider.englishFont,
              ),

              const SizedBox(height: 24),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.info.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.info,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'معلومات',
                          style: AppTypography.titleSmall.copyWith(
                            color: AppColors.info,
                            fontWeight: FontWeight.bold,
                            fontFamily: languageProvider.currentFontFamily,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• سيتم تطبيق اللغة الجديدة على جميع أجزاء التطبيق\n'
                      '• سيتم تغيير نوع الخط تلقائياً حسب اللغة المختارة\n'
                      '• العربية: خط Cairo\n'
                      '• الإنجليزية: خط Roboto',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.info,
                        height: 1.5,
                        fontFamily: languageProvider.currentFontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption({
    required LanguageProvider languageProvider,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
    required String fontFamily,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppColors.primary
              : AppColors.textTertiary.withValues(alpha: 0.3),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          title,
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            fontFamily: fontFamily,
            color: isSelected ? AppColors.primary : AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontFamily: fontFamily,
          ),
        ),
        trailing: isSelected
            ? Icon(Icons.check_circle, color: AppColors.primary, size: 24)
            : Icon(
                Icons.radio_button_unchecked,
                color: AppColors.textTertiary,
                size: 24,
              ),
        onTap: onTap,
      ),
    );
  }

  // تبويب إعدادات الإشعارات
  Widget _buildNotificationsTab(SettingsProvider settingsProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات الإشعارات',
            style: AppTypography.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // إشعارات البريد الإلكتروني
          _buildNotificationSwitch(
            settingsProvider,
            SettingKeys.emailNotifications,
            'إشعارات البريد الإلكتروني',
            'تلقي الإشعارات عبر البريد الإلكتروني',
            Icons.email,
          ),

          // إشعارات SMS
          _buildNotificationSwitch(
            settingsProvider,
            SettingKeys.smsNotifications,
            'إشعارات SMS',
            'تلقي الإشعارات عبر الرسائل النصية',
            Icons.sms,
          ),

          // الإشعارات الفورية
          _buildNotificationSwitch(
            settingsProvider,
            SettingKeys.pushNotifications,
            'الإشعارات الفورية',
            'تلقي الإشعارات الفورية في التطبيق',
            Icons.notifications,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSwitch(
    SettingsProvider settingsProvider,
    String key,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isEnabled = settingsProvider.getSettingBool(key);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primary),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: Switch(
          value: isEnabled,
          onChanged: settingsProvider.isLoading
              ? null
              : (value) {
                  settingsProvider.updateSetting(key, value.toString());
                },
        ),
      ),
    );
  }

  // حفظ إعدادات SAS Radius
  Future<void> _saveSasRadiusSettings() async {
    try {
      if (!mounted) return;

      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      final success = await settingsProvider.updateSasRadiusSettings(
        url: _sasUrlController.text.isNotEmpty ? _sasUrlController.text : null,
        ip: _sasIpController.text.isNotEmpty ? _sasIpController.text : null,
        port: _sasPortController.text.isNotEmpty
            ? _sasPortController.text
            : null,
        secret: _sasSecretController.text.isNotEmpty
            ? _sasSecretController.text
            : null,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'تم حفظ الإعدادات بنجاح' : 'فشل في حفظ الإعدادات',
            ),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات SAS: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // اختبار اتصال SAS Radius
  Future<void> _testSasRadiusConnection() async {
    try {
      if (!mounted) return;

      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      final success = await settingsProvider.testSasRadiusConnection();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'الاتصال ناجح' : 'فشل في الاتصال'),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في اختبار الاتصال: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار الاتصال: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // حفظ الإعدادات العامة
  Future<void> _saveGeneralSettings() async {
    try {
      if (!mounted) return;

      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      final settingsMap = <String, String>{};
      if (_appNameController.text.isNotEmpty) {
        settingsMap[SettingKeys.appName] = _appNameController.text;
      }
      if (_appVersionController.text.isNotEmpty) {
        settingsMap[SettingKeys.appVersion] = _appVersionController.text;
      }

      if (settingsMap.isNotEmpty) {
        final success = await settingsProvider.updateMultipleSettings(
          settingsMap,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success ? 'تم حفظ الإعدادات بنجاح' : 'فشل في حفظ الإعدادات',
              ),
              backgroundColor: success ? AppColors.success : AppColors.error,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا توجد إعدادات للحفظ')),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الإعدادات العامة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
