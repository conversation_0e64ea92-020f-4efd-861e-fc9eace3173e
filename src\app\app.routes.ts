import { Routes } from '@angular/router';
import { BlankLayoutComponent } from './layouts/blank-layout/blank-layout.component';
import { MainLayoutComponent } from './layouts/main-layout/main-layout.component';
import { authGuard } from './core/guards/auth.guard';
import { UserQrComponent } from './modules/users/user-qr/user-qr.component';
import { InvoiceDocumentComponent } from './modules/invoices/invoice-document/invoice-document.component';

export const routes: Routes = [
  {
    path: '',
    canActivateChild: [authGuard],
    component: MainLayoutComponent,
    loadChildren: () =>
      import('./modules/modules.module').then((m) => m.ModulesModule),
  },
  {
    path: 'login',
    component: BlankLayoutComponent,
    loadChildren: () =>
      import('./modules/auth/auth.module').then((m) => m.AuthModule),
  },
  {
    path: 'qr/:hashedId',
    component: UserQrComponent,
  },
  {
    path: 'invoice/:hashedId',
    component: InvoiceDocumentComponent,
  },
];
