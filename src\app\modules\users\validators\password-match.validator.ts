import { AbstractControl, ValidatorFn } from '@angular/forms';

// Custom validator to check if passwords match
export function passwordMatchValidator(): ValidatorFn {
  return (formGroup: AbstractControl): { [key: string]: any } | null => {
    const password = formGroup.get('password');
    const confirmPassword = formGroup.get('confirm_password');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    confirmPassword?.setErrors(null);
    return null;
  };
}
