import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/setting_model.dart';

class SettingsProvider extends ChangeNotifier {
  // إعدادات التطبيق المحلية
  final Map<String, String> _localSettings = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get sasRadiusUrl => _localSettings[SettingKeys.sasRadiusUrl];

  // Constructor
  SettingsProvider() {
    _clearOldSasUrls(); // مسح URLs القديمة
    _initializeSettings();
  }

  // مسح URLs القديمة لـ SAS
  Future<void> _clearOldSasUrls() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // فحص ومسح URLs القديمة
      final savedUrl = prefs.getString(SettingKeys.sasRadiusUrl) ?? '';
      if (savedUrl.contains('sas.nbtel.iq')) {
        await prefs.remove(SettingKeys.sasRadiusUrl);
        await prefs.remove(SettingKeys.sasRadiusIp);
        debugPrint('تم مسح URLs SAS القديمة من الإعدادات');
      }
    } catch (e) {
      debugPrint('خطأ في مسح URLs القديمة: $e');
    }
  }

  // تهيئة الإعدادات الافتراضية
  Future<void> _initializeSettings() async {
    try {
      _setLoading(true);

      // تحميل الإعدادات من SharedPreferences
      await _loadFromPreferences();

      // تعيين القيم الافتراضية إذا لم تكن موجودة
      _setDefaultIfEmpty(SettingKeys.sasRadiusUrl, '');
      _setDefaultIfEmpty(SettingKeys.sasRadiusIp, '');
      _setDefaultIfEmpty(SettingKeys.sasRadiusPort, '1812');
      _setDefaultIfEmpty(SettingKeys.sasRadiusSecret, '');
      _setDefaultIfEmpty(SettingKeys.appName, 'AbrajiAPIs');
      _setDefaultIfEmpty(SettingKeys.appVersion, '1.0.0');

      _clearError();
    } catch (e) {
      _setError('خطأ في تهيئة الإعدادات: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setDefaultIfEmpty(String key, String defaultValue) {
    if (!_localSettings.containsKey(key) || _localSettings[key]!.isEmpty) {
      _localSettings[key] = defaultValue;
    }
  }

  // تحميل الإعدادات من SharedPreferences
  Future<void> _loadFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (String key in [
        SettingKeys.sasRadiusUrl,
        SettingKeys.sasRadiusIp,
        SettingKeys.sasRadiusPort,
        SettingKeys.sasRadiusSecret,
        SettingKeys.appName,
        SettingKeys.appVersion,
      ]) {
        final value = prefs.getString(key);
        if (value != null) {
          _localSettings[key] = value;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات من SharedPreferences: $e');
    }
  }

  // حفظ الإعدادات في SharedPreferences
  Future<void> _saveToPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in _localSettings.entries) {
        await prefs.setString(entry.key, entry.value);
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الإعدادات في SharedPreferences: $e');
    }
  }

  // جلب قيمة إعداد بالمفتاح
  String? getSettingValue(String key, {String? defaultValue}) {
    return _localSettings[key] ?? defaultValue;
  }

  // جلب قيمة إعداد كـ boolean
  bool getSettingBool(String key, {bool defaultValue = false}) {
    final value = getSettingValue(key);
    if (value == null) return defaultValue;
    return value.toLowerCase() == 'true' || value == '1';
  }

  // جلب قيمة إعداد كـ int
  int getSettingInt(String key, {int defaultValue = 0}) {
    final value = getSettingValue(key);
    if (value == null) return defaultValue;
    return int.tryParse(value) ?? defaultValue;
  }

  // تحديث إعداد
  Future<bool> updateSetting(
    String key,
    String value, {
    String? description,
    String? type,
    bool? isPublic,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // تحديث الإعداد محلياً
      _localSettings[key] = value;

      // حفظ في SharedPreferences
      await _saveToPreferences();

      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في تحديث الإعداد: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث عدة إعدادات
  Future<bool> updateMultipleSettings(Map<String, String> settingsMap) async {
    try {
      _setLoading(true);
      _clearError();

      // تحديث الإعدادات محلياً
      for (final entry in settingsMap.entries) {
        _localSettings[entry.key] = entry.value;
      }

      // حفظ في SharedPreferences
      await _saveToPreferences();

      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في تحديث الإعدادات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث إعدادات SAS Radius
  Future<bool> updateSasRadiusSettings({
    String? url,
    String? ip,
    String? port,
    String? secret,
  }) async {
    final settingsMap = <String, String>{};

    if (url != null) settingsMap[SettingKeys.sasRadiusUrl] = url;
    if (ip != null) settingsMap[SettingKeys.sasRadiusIp] = ip;
    if (port != null) settingsMap[SettingKeys.sasRadiusPort] = port;
    if (secret != null) settingsMap[SettingKeys.sasRadiusSecret] = secret;

    return await updateMultipleSettings(settingsMap);
  }

  // اختبار الاتصال بـ SAS Radius (محاكاة)
  Future<bool> testSasRadiusConnection() async {
    try {
      _setLoading(true);
      _clearError();

      final url = _localSettings[SettingKeys.sasRadiusUrl];
      if (url == null || url.isEmpty) {
        _setError('لم يتم تعيين URL لـ SAS Radius');
        return false;
      }

      // محاكاة اختبار الاتصال
      await Future.delayed(const Duration(seconds: 2));

      // إرجاع نجاح الاتصال (محاكاة)
      return true;
    } catch (e) {
      _setError('فشل في اختبار الاتصال: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // إعادة تحميل الإعدادات
  Future<void> refresh() async {
    await _initializeSettings();
  }
}
