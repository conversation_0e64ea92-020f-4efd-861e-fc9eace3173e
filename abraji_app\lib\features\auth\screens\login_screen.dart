import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/config/vps_config.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/sas_logo_widget.dart';
import '../providers/auth_provider.dart';
import '../../settings/providers/settings_provider.dart';
import '../../settings/models/setting_model.dart';
import '../../users/providers/users_provider.dart';

class SasConnectionScreen extends StatefulWidget {
  const SasConnectionScreen({super.key});

  @override
  State<SasConnectionScreen> createState() => _SasConnectionScreenState();
}

class _SasConnectionScreenState extends State<SasConnectionScreen> {
  final _formKey = GlobalKey<FormState>();
  // Controllers for SAS Radius authentication
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  // Controller for SAS Radius connection (URL or IP)
  final _sasConnectionController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedSettings();
    _setDefaultValues(); // تعيين القيم الافتراضية الجديدة
  }

  // تعيين القيم الافتراضية الجديدة
  void _setDefaultValues() {
    // إذا لم تكن هناك قيم محفوظة، استخدم القيم الافتراضية الجديدة
    if (_sasConnectionController.text.isEmpty) {
      _sasConnectionController.text = VpsConfig.defaultSasUrl;
    }
    if (_usernameController.text.isEmpty) {
      _usernameController.text = VpsConfig.defaultUsername;
    }
    if (_passwordController.text.isEmpty) {
      _passwordController.text = VpsConfig.defaultPassword;
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _sasConnectionController.dispose();
    super.dispose();
  }

  Future<void> _connectToSas() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // حفظ إعدادات SAS Radius أولاً
    await _saveSasRadiusSettings();

    // تحديد نوع الاتصال (URL أو IP) والمنفذ الافتراضي
    String? connectionUrl;
    String connectionInput = _sasConnectionController.text.trim();

    if (connectionInput.isNotEmpty) {
      // إذا كان يحتوي على نقاط، فهو IP
      if (RegExp(r'^\d+\.\d+\.\d+\.\d+$').hasMatch(connectionInput)) {
        connectionUrl =
            'http://$connectionInput:1812'; // IP مع المنفذ الافتراضي
      } else {
        // إذا لم يحتوي على http، أضفه
        if (!connectionInput.startsWith('http://') &&
            !connectionInput.startsWith('https://')) {
          connectionUrl = 'https://$connectionInput';
        } else {
          connectionUrl = connectionInput;
        }
      }
    }

    final success = await authProvider.login(
      email: _usernameController.text,
      password: _passwordController.text,
      sasRadiusUrl: connectionUrl,
      sasRadiusIp: null, // لن نستخدمه بعد الآن
      sasRadiusPort: '1812', // المنفذ الافتراضي
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        // تهيئة UsersProvider بعد نجاح المصادقة
        final usersProvider = Provider.of<UsersProvider>(
          context,
          listen: false,
        );
        if (connectionUrl != null) {
          usersProvider.setSasRadiusConnection(connectionUrl);
        }

        context.go('/dashboard');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error ?? 'خطأ في الاتصال بـ SAS Radius'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // تحميل الإعدادات المحفوظة
  Future<void> _loadSavedSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    // تحميل آخر اتصال محفوظ
    final savedUrl =
        settingsProvider.getSettingValue(SettingKeys.sasRadiusUrl) ?? '';
    final savedIp =
        settingsProvider.getSettingValue(SettingKeys.sasRadiusIp) ?? '';

    // إعطاء الأولوية للـ URL، ثم IP
    if (savedUrl.isNotEmpty) {
      _sasConnectionController.text = savedUrl;
    } else if (savedIp.isNotEmpty) {
      _sasConnectionController.text = savedIp;
    }
  }

  // حفظ إعدادات SAS Radius
  Future<void> _saveSasRadiusSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    final connectionInput = _sasConnectionController.text.trim();
    if (connectionInput.isNotEmpty) {
      final settingsMap = <String, String>{};

      // تحديد إذا كان IP أو URL وحفظه في المكان المناسب
      if (RegExp(r'^\d+\.\d+\.\d+\.\d+$').hasMatch(connectionInput)) {
        settingsMap[SettingKeys.sasRadiusIp] = connectionInput;
        settingsMap[SettingKeys.sasRadiusUrl] = ''; // مسح URL السابق
      } else {
        settingsMap[SettingKeys.sasRadiusUrl] = connectionInput;
        settingsMap[SettingKeys.sasRadiusIp] = ''; // مسح IP السابق
      }

      // حفظ المنفذ الافتراضي
      settingsMap[SettingKeys.sasRadiusPort] = '1812';

      await settingsProvider.updateMultipleSettings(settingsMap);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary,
              AppColors.primaryLight,
              AppColors.secondary,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40),

                // Logo and Welcome
                Column(
                  children: [
                    // SAS Magic Logo
                    SasLogoWidget(
                      size: 80,
                      isConnected:
                          false, // سيتم تحديثها لاحقاً حسب حالة الاتصال
                      showText: false,
                      enableAnimation: true,
                    ),

                    const SizedBox(height: 24),

                    // Welcome Text
                    Text(
                      'ربط SAS Radius',
                      style: AppTypography.headlineLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: 8),

                    Text(
                      'قم بربط النظام مع خادم SAS Radius',
                      style: AppTypography.bodyLarge.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 48),

                // Login Form Card
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // SAS Radius Connection (URL or IP) - moved above username
                        CustomTextField(
                          controller: _sasConnectionController,
                          label: 'رابط أو IP الخادم',
                          hint: 'example.com أو *************',
                          prefixIcon: Icons.dns,
                          keyboardType: TextInputType.url,
                          textInputAction: TextInputAction.next,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال رابط الخادم أو عنوان IP';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Username Field
                        CustomTextField(
                          controller: _usernameController,
                          label: 'اسم المستخدم',
                          hint: 'أدخل اسم المستخدم',
                          prefixIcon: Icons.person_outlined,
                          textInputAction: TextInputAction.next,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال اسم المستخدم';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Password Field
                        PasswordTextField(
                          controller: _passwordController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال كلمة المرور';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 24),

                        // Login Button
                        Container(
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: AppColors.primaryGradient,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _isLoading ? null : _connectToSas,
                              borderRadius: BorderRadius.circular(16),
                              child: Center(
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : Text(
                                        'حفظ',
                                        style: AppTypography.buttonLarge
                                            .copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
