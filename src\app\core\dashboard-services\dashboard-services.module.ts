import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatsApi } from './api/stats.api';
import { StatsData } from './api/stats';
import { StatsService } from './services/stats.service';

const API = [StatsApi]

const SERVICES = [
  {provide: StatsData, useClass: StatsService}
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class DashboardServicesModule {
  static forRoot(): ModuleWithProviders<DashboardServicesModule> {
    return {
      ngModule: DashboardServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
