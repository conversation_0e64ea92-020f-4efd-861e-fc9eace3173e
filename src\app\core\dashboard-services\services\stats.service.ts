import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  Statistics,
  CardsResponse,
  TransactionsResponse,
  StatsData,
} from '../api/stats';
import { StatsApi } from '../api/stats.api';

@Injectable({
  providedIn: 'root',
})
export class StatsService implements StatsData {
  private statsApi = inject(StatsApi);

  getStats(): Observable<Statistics> {
    return this.statsApi.getStats();
  }

  getCards(): Observable<CardsResponse> {
    return this.statsApi.getCards();
  }

  getTransactions(): Observable<TransactionsResponse> {
    return this.statsApi.getTransactions();
  }
}
