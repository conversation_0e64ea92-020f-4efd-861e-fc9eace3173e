    <!-- Sessions -->
    <section class=" p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
      <div *ngIf="isLoading">
        <app-table-skeleton></app-table-skeleton>
      </div>
      <div *ngIf="!isLoading" class="shadow-sm">
      <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <!-- data table -->
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
          <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
              <th scope="col" class="px-6 py-3" *ngFor="let column of tableColumns">
                <div class="flex items-center cursor-pointer" (click)="sortByColumn(column.key)">
                  <span> {{column.value}} </span>
                  <span ><svg class="w-3 h-3 ms-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                    </svg></span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr class="bg-white border-b dark:bg-gray-900 dark:border-gray-700" *ngFor="let session of tableResponse.data">
              <td class="px-6 py-4">{{ session.acctstarttime }}</td>
              <td class="px-6 py-4">{{ session.acctstoptime }}</td>
              <td class="px-6 py-4">{{ session.framedipaddress }}</td>
              <td class="px-6 py-4">{{ session.acctinputoctets | bytesToSize }}</td>
              <td class="px-6 py-4">{{ session.acctoutputoctets | bytesToSize }}</td>
              <td class="px-6 py-4">{{ session.callingstationid }}</td>
              <td class="px-6 py-4">{{ session.calledstationid }}</td>
              <td class="px-6 py-4">{{ session.nasipaddress }}</td>
              <td class="px-6 py-4">{{ session.acctterminatecause }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <nav class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
      aria-label="Table navigation">
      <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
        Showing
        <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null? tableResponse.from
          :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
        of
        <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
      </span>
      <!-- Pagination controls -->
      <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
        <li>
          <button (click)="changePage((tableResponse.current_page - 1).toString())"
            [disabled]="tableResponse.current_page === 1"
            class="flex items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
            <span class="sr-only">Previous</span>
            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </button>
        </li>
        <ng-container *ngFor="let page of getPagesToDisplay()">
          <li *ngIf="page === '...'">
            <span
              class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
          </li>
          <li *ngIf="page !== '...'">
            <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
              [class.text-primary-600]="tableResponse.current_page === page"
              [class.z-10]="tableResponse.current_page === page"
              class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
              {{ page }}
            </button>
          </li>
        </ng-container>
        <li>
          <button (click)="changePage((tableResponse.current_page + 1).toString())"
            [disabled]="tableResponse.current_page === tableResponse.last_page"
            class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
            <span class="sr-only">Next</span>
            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                clip-rule="evenodd" />
            </svg>

          </button>
        </li>
      </ul>
    </nav>
  </div>
    </section>



