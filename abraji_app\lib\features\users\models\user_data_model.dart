class UserData {
  final String id;
  final String username;
  final String? fullName;
  final String? email;
  final String? phone;
  final String? address;
  final String status; // active, inactive, suspended, expired
  final String? plan;
  final DateTime? createdAt;
  final DateTime? expiryDate;
  final DateTime? lastLogin;
  final String? ipAddress;
  final String? macAddress;
  final double? balance;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  UserData({
    required this.id,
    required this.username,
    this.fullName,
    this.email,
    this.phone,
    this.address,
    required this.status,
    this.plan,
    this.createdAt,
    this.expiryDate,
    this.lastLogin,
    this.ipAddress,
    this.macAddress,
    this.balance,
    this.notes,
    this.additionalData,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id']?.toString() ?? '',
      username: json['username'] ?? '',
      fullName: json['full_name'] ?? json['fullName'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      status: json['status'] ?? 'unknown',
      plan: json['plan'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      expiryDate: json['expiry_date'] != null
          ? DateTime.tryParse(json['expiry_date'])
          : null,
      lastLogin: json['last_login'] != null
          ? DateTime.tryParse(json['last_login'])
          : null,
      ipAddress: json['ip_address'] ?? json['ipAddress'],
      macAddress: json['mac_address'] ?? json['macAddress'],
      balance: json['balance']?.toDouble(),
      notes: json['notes'],
      additionalData: json['additional_data'] ?? json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'address': address,
      'status': status,
      'plan': plan,
      'created_at': createdAt?.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'ip_address': ipAddress,
      'mac_address': macAddress,
      'balance': balance,
      'notes': notes,
      'additional_data': additionalData,
    };
  }

  // Helper methods
  bool get isActive => status.toLowerCase() == 'active';
  bool get isExpired =>
      expiryDate != null && expiryDate!.isBefore(DateTime.now());
  bool get isSuspended => status.toLowerCase() == 'suspended';

  String get statusDisplayName {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'suspended':
        return 'معلق';
      case 'expired':
        return 'منتهي الصلاحية';
      default:
        return 'غير معروف';
    }
  }

  String get displayName => fullName ?? username;

  UserData copyWith({
    String? id,
    String? username,
    String? fullName,
    String? email,
    String? phone,
    String? address,
    String? status,
    String? plan,
    DateTime? createdAt,
    DateTime? expiryDate,
    DateTime? lastLogin,
    String? ipAddress,
    String? macAddress,
    double? balance,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return UserData(
      id: id ?? this.id,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      status: status ?? this.status,
      plan: plan ?? this.plan,
      createdAt: createdAt ?? this.createdAt,
      expiryDate: expiryDate ?? this.expiryDate,
      lastLogin: lastLogin ?? this.lastLogin,
      ipAddress: ipAddress ?? this.ipAddress,
      macAddress: macAddress ?? this.macAddress,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'UserData(id: $id, username: $username, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// نموذج لاستجابة قائمة المستخدمين
class UsersResponse {
  final bool success;
  final String message;
  final List<UserData> data;
  final int total;
  final int currentPage;
  final int totalPages;
  final Map<String, dynamic>? meta;

  UsersResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.total,
    this.currentPage = 1,
    this.totalPages = 1,
    this.meta,
  });

  factory UsersResponse.fromJson(Map<String, dynamic> json) {
    return UsersResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => UserData.fromJson(item))
              .toList() ??
          [],
      total: json['total'] ?? 0,
      currentPage: json['current_page'] ?? 1,
      totalPages: json['total_pages'] ?? 1,
      meta: json['meta'],
    );
  }
}

// نموذج لإحصائيات المستخدمين
class UserStats {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final int suspendedUsers;
  final int expiredUsers;
  final double totalBalance;
  final int onlineUsers;

  UserStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.suspendedUsers,
    required this.expiredUsers,
    required this.totalBalance,
    required this.onlineUsers,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalUsers: json['total_users'] ?? 0,
      activeUsers: json['active_users'] ?? 0,
      inactiveUsers: json['inactive_users'] ?? 0,
      suspendedUsers: json['suspended_users'] ?? 0,
      expiredUsers: json['expired_users'] ?? 0,
      totalBalance: (json['total_balance'] ?? 0).toDouble(),
      onlineUsers: json['online_users'] ?? 0,
    );
  }
}

// فلاتر البحث
class UserFilters {
  final String? search;
  final String? status;
  final String? plan;
  final DateTime? createdFrom;
  final DateTime? createdTo;
  final DateTime? expiryFrom;
  final DateTime? expiryTo;
  final int page;
  final int limit;
  final String sortBy;
  final String sortOrder;

  UserFilters({
    this.search,
    this.status,
    this.plan,
    this.createdFrom,
    this.createdTo,
    this.expiryFrom,
    this.expiryTo,
    this.page = 1,
    this.limit = 20,
    this.sortBy = 'created_at',
    this.sortOrder = 'desc',
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_order': sortOrder,
    };

    if (search != null && search!.isNotEmpty) {
      params['search'] = search;
    }
    if (status != null && status!.isNotEmpty) {
      params['status'] = status;
    }
    if (plan != null && plan!.isNotEmpty) {
      params['plan'] = plan;
    }
    if (createdFrom != null) {
      params['created_from'] = createdFrom!.toIso8601String();
    }
    if (createdTo != null) {
      params['created_to'] = createdTo!.toIso8601String();
    }
    if (expiryFrom != null) {
      params['expiry_from'] = expiryFrom!.toIso8601String();
    }
    if (expiryTo != null) {
      params['expiry_to'] = expiryTo!.toIso8601String();
    }

    return params;
  }

  UserFilters copyWith({
    String? search,
    String? status,
    String? plan,
    DateTime? createdFrom,
    DateTime? createdTo,
    DateTime? expiryFrom,
    DateTime? expiryTo,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
  }) {
    return UserFilters(
      search: search ?? this.search,
      status: status ?? this.status,
      plan: plan ?? this.plan,
      createdFrom: createdFrom ?? this.createdFrom,
      createdTo: createdTo ?? this.createdTo,
      expiryFrom: expiryFrom ?? this.expiryFrom,
      expiryTo: expiryTo ?? this.expiryTo,
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}
