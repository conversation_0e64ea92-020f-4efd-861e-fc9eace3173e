import { Component, OnInit } from '@angular/core';
import { UserDetails } from '../../../core/user-services/api/users';
import { UsersService } from '../../../core/user-services/services/users.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '../../shared/toast/toast.service'; // Adjust the import path as needed
import * as CryptoJS from 'crypto-js';
import { EncryptionService } from '../../../core/common-services/services/encryption.service';

@Component({
  selector: 'app-user-overview',
  templateUrl: './user-overview.component.html',
  styleUrls: ['./user-overview.component.scss'],
})
export class UserOverviewComponent implements OnInit {
  private userId!: string | null;
  userDetails: UserDetails | null = null;
  isLoading = false;
  showQr = false;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private usersService: UsersService,
    private toastService: ToastService,
    private encryptionService: EncryptionService
  ) {}

  ngOnInit(): void {
    // Access parent route to get the id parameter
    this.route.parent?.paramMap.subscribe((params) => {
      this.userId = params.get('id');

      if (this.userId) {
        this.getUserDetails();
      }
    });
  }
  toggleQr() {
    this.showQr = !this.showQr;
  }

  redirectQR() {
    const hashedId = this.encryptionService.encryptQR(String(this.userId));
    this.router.navigate(['/qr', hashedId]);
  }

  getUserDetails() {
    // fetch the data
    if (this.userId) {
      this.isLoading = true;
      this.usersService.getUser(this.userId).subscribe({
        next: (response: UserDetails) => {
          this.userDetails = response;
          console.log('response ', response);
          this.isLoading = false;
        },
        error: (error) => {
          this.isLoading = false;
          console.error(error);
          // Show error toast
          this.toastService.addToast(
            'error',
            'Error loading user details',
            error.errorMessage
          );
        },
      });
    }
  }
}
