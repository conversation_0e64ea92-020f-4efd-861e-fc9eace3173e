import { Component } from '@angular/core';
import {
  ExpensesForm,
  Expense,
  initialExpenseColumnsState,
} from '../../../core/expenses-services/api/expenses';
import {
  ColumnState,
  TableResponse,
} from '../../../core/common-services/interfaces/table-response';
import { ExpensesService } from '../../../core/expenses-services/services/expenses.service';
import { ToastService } from '../../shared/toast/toast.service';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { format } from 'date-fns';


@Component({
  selector: 'app-expenses',
  templateUrl: './expenses.component.html',
  styleUrl: './expenses.component.scss',
})
export class ExpensesComponent {
  expenses: Expense[] = [];
  selectedExpense!: Expense;
  requestForm!: ExpensesForm;
  isLoading: boolean = false;
  expensesColumnsState!: ColumnState[];
  createExpenseForm!: FormGroup;
  editExpenseForm!: FormGroup;
  tableResponse: TableResponse<Expense> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };

  constructor(
    private expensesService: ExpensesService,
    private toastService: ToastService,
    private tableElementsService: TableElementsService,
    private localStorageService: LocalStorageService,
    private fb: FormBuilder
  ) {}

  // Bind search value to form
  searchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.requestForm.search = inputElement.value;
    this.requestForm.page = 1;
    this.loadExpenses();
  }

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.requestForm);
    // fetch data
    this.loadExpenses();
  }

  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(
      this.tableResponse.last_page,
      this.requestForm.page
    );
  }

  // Bind the change page in pagination to the form
  changePage(page: string | number): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.requestForm.page = pageNumber;
      this.loadExpenses();
    }
  }

  // Bind the change in columns visibility
  toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(columnKey, this.expensesColumnsState, this.localStorageService.ExpensesColumnsState);
  }

  ngOnInit(): void {
    // initiate columns state
    this.expensesColumnsState = this.localStorageService
    .loadColumnsState(this.localStorageService.ExpensesColumnsState)
    || initialExpenseColumnsState;

    this.requestForm = {
      page: 1,
      count: 10,
      sortBy: '',
      direction: '',
      search: '',
      columns: [...this.getVisibleColumns(), 'id'],
    };

    // fetch users
    this.loadExpenses();
    // initiate forms
    this.initCreateExpenseForm();
    this.initEditExpenseForm();
  }

  getVisibleColumns(): string[] {
    const columns = this.tableElementsService.getVisibleColumns(this.expensesColumnsState);
    console.log("columns", columns);

    return columns;
  }

  getPropertyValue(item: Expense, key: string): any {
    switch (key) {
      default:
        return item[key as keyof Expense];
    }
  }

  loadExpenses(): void {
    this.isLoading = true;
    this.expensesService.getExpenses(this.requestForm).subscribe({
      next: (response: any) => {
        console.log(response);
        this.tableResponse = response;
        this.isLoading = false;
      },
      error: (error: any) => {
        this.isLoading = false;
        this.toastService.addToast(
          'error',
          'Error Message',
          'There was an error on fetching the data'
        );
        console.error('There was an error!', error);
      },
    });
  }

  selectExpense(expense: Expense) {
    this.selectedExpense = expense;
    this.editExpenseForm.get('amount')?.setValue(expense.cost);
    this.editExpenseForm.get('description')?.setValue(expense.description);
    this.editExpenseForm.get('category')?.setValue(expense.category);
    console.log("selectedExpense", this.editExpenseForm.value);

    }

  createNewExpense(): void {
    if (this.createExpenseForm.invalid) {
      this.toastService.addToast(
        'error',
        'Error Message',
        'Please fill in all required fields'
      );
      return;
    }
    const expense = this.prepareNewExpense(this.createExpenseForm);
    console.log("expense", expense);

    this.expensesService.createExpense(expense).subscribe({
      next: (response: any) => {
        this.toastService.addToast(
          'success',
          'Success Message',
          'Expense created successfully'
        );
        console.log(response);

        this.loadExpenses();
      },
      error: (error: any) => {
        this.toastService.addToast(
          'error',
          'Error Message',
          'There was an error on creating the expense'
        );
        console.error('There was an error!', error);
      },
    });

  }

  private initCreateExpenseForm(): void {
    this.createExpenseForm = this.fb.group({
      category: ['', Validators.required],
      description: [''],
      amount: ['', Validators.required, Validators.min(0)],
    });
  }

  private initEditExpenseForm(): void {
    this.editExpenseForm = this.fb.group({
      category: ['', Validators.required],
      description: [''],
      amount: ['', Validators.required, Validators.min(0)],
    });
  }

  private prepareNewExpense(form: any): any {
    const credentials = this.localStorageService.getCredentials();
    const expense = {
      created_by: credentials.username,
      admin_id: credentials.id,
      cost: form.value.amount,
      description: form.value.description,
      date: format(new Date(), 'yyyy-MM-dd'),
      type: 'out',
      category: form.value.category,
    };
    return expense;
  }



  // getters
  get createFormAmount(): any {
    return this.createExpenseForm.get('amount');
  }

  get createFormCategory(): any {
    return this.createExpenseForm.get('category');
  }

}
