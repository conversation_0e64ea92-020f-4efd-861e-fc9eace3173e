import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/user_data_model.dart';

class UserQRScreen extends StatefulWidget {
  final UserData user;

  const UserQRScreen({super.key, required this.user});

  @override
  State<UserQRScreen> createState() => _UserQRScreenState();
}

class _UserQRScreenState extends State<UserQRScreen> {
  String qrData = '';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _generateQRData();
  }

  void _generateQRData() {
    setState(() {
      isLoading = true;
    });

    // محاكاة توليد بيانات QR
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        qrData = _buildQRData();
        isLoading = false;
      });
    });
  }

  String _buildQRData() {
    // بناء بيانات QR بتنسيق JSON
    final qrInfo = {
      'type': 'user_credentials',
      'user_id': widget.user.id,
      'username': widget.user.username,
      'plan': widget.user.plan ?? 'basic',
      'expires_at': widget.user.expiryDate?.toIso8601String(),
      'generated_at': DateTime.now().toIso8601String(),
    };

    // تحويل إلى نص JSON
    return '''
{
  "type": "${qrInfo['type']}",
  "user_id": "${qrInfo['user_id']}",
  "username": "${qrInfo['username']}",
  "plan": "${qrInfo['plan']}",
  "expires_at": "${qrInfo['expires_at'] ?? ''}",
  "generated_at": "${qrInfo['generated_at']}"
}''';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('QR Code - ${widget.user.displayName}'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateQRData,
            tooltip: 'إعادة توليد',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareQR,
            tooltip: 'مشاركة',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // معلومات المستخدم
                  _buildUserInfo(),

                  const SizedBox(height: 24),

                  // QR Code
                  _buildQRCode(),

                  const SizedBox(height: 24),

                  // معلومات QR
                  _buildQRInfo(),

                  const SizedBox(height: 24),

                  // أزرار الإجراءات
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildUserInfo() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: Text(
                widget.user.displayName.isNotEmpty
                    ? widget.user.displayName[0].toUpperCase()
                    : 'U',
                style: AppTypography.titleLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.user.displayName,
                    style: AppTypography.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '@${widget.user.username}',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.user.statusDisplayName,
                      style: AppTypography.bodySmall.copyWith(
                        color: _getStatusColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCode() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'QR Code للمستخدم',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: QrImageView(
              data: qrData,
              version: QrVersions.auto,
              size: 200.0,
              backgroundColor: Colors.white,
              dataModuleStyle: const QrDataModuleStyle(
                dataModuleShape: QrDataModuleShape.square,
                color: Colors.black,
              ),
              eyeStyle: const QrEyeStyle(
                eyeShape: QrEyeShape.square,
                color: Colors.black,
              ),
              errorCorrectionLevel: QrErrorCorrectLevel.M,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'امسح الكود للحصول على معلومات المستخدم',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQRInfo() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات QR Code',
              style: AppTypography.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('معرف المستخدم', widget.user.id),
            _buildInfoRow('اسم المستخدم', widget.user.username),
            _buildInfoRow('الخطة', widget.user.plan ?? 'أساسية'),
            _buildInfoRow('الحالة', widget.user.statusDisplayName),
            if (widget.user.expiryDate != null)
              _buildInfoRow(
                'تاريخ الانتهاء',
                _formatDate(widget.user.expiryDate!),
              ),
            _buildInfoRow('تاريخ التوليد', _formatDate(DateTime.now())),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _copyQRData,
                icon: const Icon(Icons.copy),
                label: const Text('نسخ البيانات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _saveQR,
                icon: const Icon(Icons.download),
                label: const Text('حفظ الصورة'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _printQR,
            icon: const Icon(Icons.print),
            label: const Text('طباعة QR Code'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (widget.user.status.toLowerCase()) {
      case 'active':
        return AppColors.success;
      case 'suspended':
        return AppColors.warning;
      case 'expired':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareQR() {
    // تنفيذ مشاركة QR
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم مشاركة QR Code')));
  }

  void _copyQRData() {
    Clipboard.setData(ClipboardData(text: qrData));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم نسخ بيانات QR Code')));
  }

  void _saveQR() {
    // تنفيذ حفظ صورة QR
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم حفظ صورة QR Code')));
  }

  void _printQR() {
    // تنفيذ طباعة QR
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم طباعة QR Code')));
  }
}
