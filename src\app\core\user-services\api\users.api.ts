import { inject, Injectable } from "@angular/core";
import { HttpService } from "../../common-services/services/http.service";
import { catchError, Observable, of, shareReplay } from "rxjs";
import { UserDetails } from "./users";
import { CacheService } from '../../common-services/services/cache.service';

@Injectable({
  providedIn: 'root'
})
export class UserApi {
  private apiUrl = 'users';
  private httpService = inject(HttpService);
  private cacheService = inject(CacheService);
  private userCache = new Map<string, Observable<UserDetails>>();

  constructor() {
    this.cacheService.registerCacheClearCallback(() => this.clearCache());
  }

  getUsers(payload: string) {
    return this.httpService.post(this.apiUrl + '/table', { payload });
  }

  getOnlineUsers(payload: string) {
    return this.httpService.post(this.apiUrl + '/online', { payload });
  }

  getUser(id: string): Observable<UserDetails> {
    if (!this.userCache.has(id)) {
      const url = this.apiUrl + `/get/${id}`;
      const userObservable = this.httpService.get(url).pipe(
        shareReplay(1), // Cache the latest response
        catchError(error => {
          this.userCache.delete(id); // Reset cache on error
          return of(error);
        })
      );
      this.userCache.set(id, userObservable);
    }
    return this.userCache.get(id)!;
  }

  getUserSessions(id: string, payload: string): Observable<any> {
    return this.httpService.post(this.apiUrl + `/sessions/${id}`, { payload });
  }

  getUserTraffic(payload: string): Observable<any> {
    return this.httpService.post(this.apiUrl + `/traffic`, { payload });
  }

  editUser(userId: string | null, payload: string) {
    const url = this.apiUrl + `/edit/${userId}`;
    return this.httpService.put(url, { payload });
  }

  createUser(payload: string): Observable<any> {
    const url = this.apiUrl + `/create`;
    return this.httpService.post(url, { payload });
  }

  clearCache(): void {
    this.userCache.clear();
  }
}
