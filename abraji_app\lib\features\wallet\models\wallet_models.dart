class WalletTransaction {
  final String id;
  final String userId;
  final String type; // 'deposit', 'withdrawal', 'payment', 'refund'
  final double amount;
  final String description;
  final DateTime createdAt;
  final String status; // 'pending', 'completed', 'failed', 'cancelled'
  final String? reference;
  final Map<String, dynamic>? metadata;

  WalletTransaction({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.description,
    required this.createdAt,
    required this.status,
    this.reference,
    this.metadata,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) {
    return WalletTransaction(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      type: json['type'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      description: json['description'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'pending',
      reference: json['reference'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type,
      'amount': amount,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'status': status,
      'reference': reference,
      'metadata': metadata,
    };
  }

  String get typeDisplayName {
    switch (type) {
      case 'deposit':
        return 'إيداع';
      case 'withdrawal':
        return 'سحب';
      case 'payment':
        return 'دفع';
      case 'refund':
        return 'استرداد';
      default:
        return type;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'معلق';
      case 'completed':
        return 'مكتمل';
      case 'failed':
        return 'فاشل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  bool get isCredit => type == 'deposit' || type == 'refund';
  bool get isDebit => type == 'withdrawal' || type == 'payment';
}

class WalletBalance {
  final String userId;
  final double currentBalance;
  final double totalDeposits;
  final double totalWithdrawals;
  final double totalPayments;
  final double totalRefunds;
  final DateTime lastUpdated;

  WalletBalance({
    required this.userId,
    required this.currentBalance,
    required this.totalDeposits,
    required this.totalWithdrawals,
    required this.totalPayments,
    required this.totalRefunds,
    required this.lastUpdated,
  });

  factory WalletBalance.fromJson(Map<String, dynamic> json) {
    return WalletBalance(
      userId: json['user_id'] ?? '',
      currentBalance: (json['current_balance'] ?? 0).toDouble(),
      totalDeposits: (json['total_deposits'] ?? 0).toDouble(),
      totalWithdrawals: (json['total_withdrawals'] ?? 0).toDouble(),
      totalPayments: (json['total_payments'] ?? 0).toDouble(),
      totalRefunds: (json['total_refunds'] ?? 0).toDouble(),
      lastUpdated: DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'current_balance': currentBalance,
      'total_deposits': totalDeposits,
      'total_withdrawals': totalWithdrawals,
      'total_payments': totalPayments,
      'total_refunds': totalRefunds,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  double get totalIncome => totalDeposits + totalRefunds;
  double get totalExpense => totalWithdrawals + totalPayments;
  double get netFlow => totalIncome - totalExpense;
}

class CreateTransactionRequest {
  final String userId;
  final String type;
  final double amount;
  final String description;
  final String? reference;
  final Map<String, dynamic>? metadata;

  CreateTransactionRequest({
    required this.userId,
    required this.type,
    required this.amount,
    required this.description,
    this.reference,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'type': type,
      'amount': amount,
      'description': description,
      'reference': reference,
      'metadata': metadata,
    };
  }
}

class TransactionFilters {
  final String? userId;
  final String? type;
  final String? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;
  final String? search;
  final String sortBy;
  final String sortOrder;
  final int page;
  final int limit;

  TransactionFilters({
    this.userId,
    this.type,
    this.status,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
    this.search,
    this.sortBy = 'created_at',
    this.sortOrder = 'desc',
    this.page = 1,
    this.limit = 20,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (userId != null) params['user_id'] = userId;
    if (type != null) params['type'] = type;
    if (status != null) params['status'] = status;
    if (startDate != null) params['start_date'] = startDate!.toIso8601String();
    if (endDate != null) params['end_date'] = endDate!.toIso8601String();
    if (minAmount != null) params['min_amount'] = minAmount;
    if (maxAmount != null) params['max_amount'] = maxAmount;
    if (search != null) params['search'] = search;
    
    params['sort_by'] = sortBy;
    params['sort_order'] = sortOrder;
    params['page'] = page;
    params['limit'] = limit;
    
    return params;
  }

  TransactionFilters copyWith({
    String? userId,
    String? type,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? limit,
  }) {
    return TransactionFilters(
      userId: userId ?? this.userId,
      type: type ?? this.type,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      search: search ?? this.search,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      page: page ?? this.page,
      limit: limit ?? this.limit,
    );
  }
}

class TransactionsResponse {
  final List<WalletTransaction> transactions;
  final int total;
  final int page;
  final int limit;
  final bool hasMore;

  TransactionsResponse({
    required this.transactions,
    required this.total,
    required this.page,
    required this.limit,
    required this.hasMore,
  });

  factory TransactionsResponse.fromJson(Map<String, dynamic> json) {
    return TransactionsResponse(
      transactions: (json['data'] as List<dynamic>?)
          ?.map((item) => WalletTransaction.fromJson(item))
          .toList() ?? [],
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      hasMore: json['has_more'] ?? false,
    );
  }
}

// أنواع المعاملات المتاحة
enum TransactionType {
  deposit,
  withdrawal,
  payment,
  refund,
}

extension TransactionTypeExtension on TransactionType {
  String get value {
    switch (this) {
      case TransactionType.deposit:
        return 'deposit';
      case TransactionType.withdrawal:
        return 'withdrawal';
      case TransactionType.payment:
        return 'payment';
      case TransactionType.refund:
        return 'refund';
    }
  }

  String get displayName {
    switch (this) {
      case TransactionType.deposit:
        return 'إيداع';
      case TransactionType.withdrawal:
        return 'سحب';
      case TransactionType.payment:
        return 'دفع';
      case TransactionType.refund:
        return 'استرداد';
    }
  }

  static TransactionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'deposit':
        return TransactionType.deposit;
      case 'withdrawal':
        return TransactionType.withdrawal;
      case 'payment':
        return TransactionType.payment;
      case 'refund':
        return TransactionType.refund;
      default:
        return TransactionType.payment;
    }
  }

  static List<TransactionType> get allTypes => TransactionType.values;
}

// حالات المعاملات المتاحة
enum TransactionStatus {
  pending,
  completed,
  failed,
  cancelled,
}

extension TransactionStatusExtension on TransactionStatus {
  String get value {
    switch (this) {
      case TransactionStatus.pending:
        return 'pending';
      case TransactionStatus.completed:
        return 'completed';
      case TransactionStatus.failed:
        return 'failed';
      case TransactionStatus.cancelled:
        return 'cancelled';
    }
  }

  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'معلق';
      case TransactionStatus.completed:
        return 'مكتمل';
      case TransactionStatus.failed:
        return 'فاشل';
      case TransactionStatus.cancelled:
        return 'ملغي';
    }
  }

  static TransactionStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return TransactionStatus.pending;
      case 'completed':
        return TransactionStatus.completed;
      case 'failed':
        return TransactionStatus.failed;
      case 'cancelled':
        return TransactionStatus.cancelled;
      default:
        return TransactionStatus.pending;
    }
  }

  static List<TransactionStatus> get allStatuses => TransactionStatus.values;
}
