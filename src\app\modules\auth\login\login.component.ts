import { Component, inject, OnInit } from '@angular/core';
import { AuthService } from '../../../core/auth-services/services/auth.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { loginDTO } from '../../../core/auth-services/api/auth';
import { Router } from '@angular/router';
import { ToastService } from '../../shared/toast/toast.service'; // Import ToastService
import { ManagerModel } from '../../../core/user-services/api/header';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  private service = inject(AuthService);
  private router = inject(Router);
  private toastService = inject(ToastService);
  private localStorageService = inject(LocalStorageService);
  errorMessage: string | null = null;
  loginForm!: FormGroup;
  fb = inject(FormBuilder);
  isLoading = false;

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      username: this.fb.control('', [Validators.required]),
      password: this.fb.control('', [Validators.required]),
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      const credentials: loginDTO = { username: this.username?.value, password: this.password?.value };
      this.login(credentials);
    } else {
      console.log('Form not valid');
    }
  }

  login(credentials: loginDTO ): void {
    this.isLoading = true
    this.service.performLogin(credentials).subscribe({
      next: (response) => {
        this.errorMessage = null;
        this.toastService.addToast('success', 'Login Successful', 'You have successfully logged in.');
        //this.updateUserAccounts(credentials.username, credentials.password, Number(this.service.getAuthId()));

        // Logic to refresh the page
        const redirectTo = this.service.redirectTo || '/';
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([redirectTo]);
        });
      },
      complete: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = 'An error occurred. Please try again later.';
        this.toastService.addToast('error', 'Login Failed', error.error);
      },
    });
  }



  get username(): any {
    return this.loginForm.get('username');
  }
  get password(): any {
    return this.loginForm.get('password');
  }
}
