import { Component, OnInit } from '@angular/core';
import { WalletService } from '../../../core/wallet-services/services/wallet.service';
import { ToastService } from '../../shared/toast/toast.service';
import { TableResponse } from '../../../core/common-services/interfaces/table-response';
import { Wallet, WalletTransaction, WalletTransactionsForm } from '../../../core/wallet-services/api/wallet';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ExpensesService } from '../../../core/expenses-services/services/expenses.service';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { format } from 'date-fns';

@Component({
  selector: 'app-wallet-home',
  templateUrl: './wallet-home.component.html',
  styleUrl: './wallet-home.component.scss'
})
export class WalletHomeComponent implements OnInit {
  transactionIsLoading = false;
  isLoading = false;
  depositForm!: FormGroup;
  withdrawForm!: FormGroup;
  editBalanceForm!: FormGroup;
  resetBalanceForm!: FormGroup;
  balance: number = 0;
  transactions: TableResponse<WalletTransaction> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };

  constructor(
    private walletService: WalletService,
    private expensesService: ExpensesService,
    private localStorageService: LocalStorageService,
    private toastService: ToastService,
    private fb: FormBuilder,

  ) { }

  async ngOnInit() {
    // initiate forms
    this.initDepositForm();
    this.initWithdrawForm();
    this.initEditBalanceForm();
    // get balance and transactions
    this.getBalance();
    this.getTransactions();
  }

  getBalance() {
    this.isLoading = true;
    this.walletService.getBalance().subscribe({
      next: (response) => {
        this.balance = response.data.balance;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error', error.error.message);
        this.isLoading = false;
      }
    });
  }

  getTransactions() {
    this.transactionIsLoading = true;
    const transactionsForm = this.initiateTransactionsForm();
    this.walletService.getTransactions(transactionsForm).subscribe({
      next: (response) => {
        this.transactions = response.data;
        console.log(response);
        this.transactionIsLoading = false;
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error', error.error.message);
        this.transactionIsLoading = false;
      }
    });
  }

  private initiateTransactionsForm(): WalletTransactionsForm {
    const transactionsForm: WalletTransactionsForm = {
      page: 1,
      count: 4,
      sortBy: 'created_at',
      direction: 'desc',
      search: '',
      columns: []
    };
    return transactionsForm;
  }

  private initDepositForm(){
    this.depositForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(1)]],
    });
  }

  private initWithdrawForm(){
    this.withdrawForm = this.fb.group({
      category: ['', Validators.required],
      description: [''],
      amount: ['', Validators.required, Validators.min(0)],
    });
  }

  private initEditBalanceForm(){
    this.editBalanceForm = this.fb.group({
      balance: ['', [Validators.required, Validators.min(0)]],
    });
  }

  updateEditBalanceForm(){
    this.editBalanceForm.patchValue({
      balance: this.balance,
    });
  }

  resetBalance(){
    this.walletService.resetBalance().subscribe({
      next: (response) => {
        this.toastService.addToast('success', 'Success', 'Balance reset successfully');
        this.getBalance();
        this.getTransactions();
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error', error.error.message);
      }
    });
  }

  editBalance(){
    const balance = this.editBalanceForm.value.balance;
    console.log(balance);
    // handle balance not changes case
    if (balance === this.balance){
      this.toastService.addToast('success', 'Success', 'Balance updated successfully');
      return;
    }
    this.walletService.editWallet(balance).subscribe({
      next: (response) => {
        this.toastService.addToast('success', 'Success', 'Balance updated successfully');
        this.getBalance();
        this.getTransactions();
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error', error.error.message);
      }
    });
  }

  deposit(){
    const amount = this.depositForm.value.amount;
    this.walletService.deposit(amount).subscribe({
      next: (response) => {
        this.toastService.addToast('success', 'Success', 'Deposit successful');

        this.getBalance();
        this.getTransactions();
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error', error.error.message);
      }
    });
  }

  withdraw(): void {
    if (this.withdrawForm.invalid) {
      this.toastService.addToast(
        'error',
        'Error Message',
        'Please fill in all required fields'
      );
      return;
    }
    const expense = this.prepareNewExpense(this.withdrawForm);

    this.expensesService.createExpense(expense).subscribe({
      next: (response: any) => {
        this.toastService.addToast(
          'success',
          'Success Message',
          'Expense created successfully'
        );
        this.getBalance();
        this.getTransactions();
      },
      error: (error: any) => {
        this.toastService.addToast(
          'error',
          'Error Message',
          'There was an error on creating the expense'
        );
        console.error('There was an error!', error);
      },
    });

  }

  private prepareNewExpense(form: any): any {
    const credentials = this.localStorageService.getCredentials();
    const expense = {
      created_by: credentials.username,
      admin_id: credentials.id,
      cost: form.value.amount,
      description: form.value.description,
      date: format(new Date(), 'yyyy-MM-dd'),
      type: 'out',
      category: form.value.category,
    };
    return expense;
  }


  get depositFormAmount():any {
    return this.depositForm.get('amount');
  }

  get withdrawFormAmount():any {
    return this.withdrawForm.get('amount');
  }

  get withdrawFormCategory():any {
    return this.withdrawForm.get('amount');
  }

  get editBalanceFormBalance():any {
    return this.editBalanceForm.get('balance');
  }
}
