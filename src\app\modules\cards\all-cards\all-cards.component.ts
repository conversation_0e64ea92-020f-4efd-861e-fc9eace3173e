import { Component, OnInit } from '@angular/core';
import { SeriesService } from '../../../core/cards-services/services/series.service';
import { TableResponse } from '../../../core/common-services/interfaces/table-response';
import { ToastService } from '../../shared/toast/toast.service';
import { ColumnState } from '../../../core/user-services/api/users';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { SeriesDetails, initialCardColumnsState, SeriesForm } from '../../../core/cards-services/api/series';
import { ProfileService } from '../../../core/service-profile/services/profile.service';
import { Service } from '../../../core/service-profile/api/profile';
import { Router } from '@angular/router';
import { ExcelService } from '../../../core/common-services/services/excel.service';

@Component({
  selector: 'app-all-cards',
  templateUrl: './all-cards.component.html',
  styleUrls: ['./all-cards.component.scss'],
})
export class AllCardsComponent implements OnInit {
  currentPage: number = 1;
  requestForm!: SeriesForm;
  isLoading: boolean = false;
  quotas!: Service[];
  cardColumnsState!: ColumnState[];
  tableResponse: TableResponse<SeriesDetails> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };
  exportIsLoading: boolean = false;

  constructor(
    private seriesService: SeriesService,
    private toastService: ToastService,
    private tableElementsService: TableElementsService,
    private localStorageService: LocalStorageService,
    private profileService: ProfileService,
    private excelService: ExcelService,
  ) {}

  getQuotas(): void {
    if (this.quotas) return;
    console.log('getQ');
    this.profileService.getServices(0).subscribe({
      next: (response) => {
        this.quotas = response;
      },
      error: (error) => {
        this.toastService.addToast('error', 'Error Fetching Profiles', error);
      }
    })
  }


  // Bind search value to form
  searchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.requestForm.search = inputElement.value;
    this.requestForm.page = 1;
    this.loadSeries();
  }

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.requestForm);
    // fetch data
    this.loadSeries();
  }


  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(this.tableResponse.last_page, this.requestForm.page);
  }

  // Bind the change page in pagination to the form
  changePage(page: (string | number)): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.requestForm.page = pageNumber;
      this.loadSeries();
    }
  }

  // Bind the change in columns visibility
  toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(columnKey, this.cardColumnsState, this.localStorageService.CardColumnsState);
  }

  ngOnInit(): void {
    // initiate columns state
    this.cardColumnsState = this.localStorageService
      .loadColumnsState(this.localStorageService.CardColumnsState)
      || initialCardColumnsState;

    this.requestForm = {
      page: 1,
      count: 10,
      sortBy: "",
      direction: "",
      search: "",
      columns: this.getVisibleColumns(),
    };

    const navigationData = history.state.data;
    if (navigationData) {
      console.log('Navigation Data:', navigationData);
      this.requestForm = { ...this.requestForm, ...navigationData };
    }

    // fetch users
    this.loadSeries();
  }

  getVisibleColumns(): string[] {
    return this.tableElementsService.getVisibleColumns(this.cardColumnsState);
  }

  getPropertyValue(series: SeriesDetails, key: string): any {
    switch (key) {
      case 'owner':
        return series.owner_details?.username;
      case 'profile':
        return series.profile_details?.name;
      case 'type':
        return series.type === 0 ? 'Refill Card' : 'Prepaid Card';
      default:
        return series[key as keyof SeriesDetails];
    }
  }

  loadSeries(): void {
    this.isLoading = true;
    this.seriesService.getSeries(this.requestForm).subscribe({
      next: (response: any) => {
        console.log(response);
        this.tableResponse = response;
        this.isLoading = false;
    },
    error: (error: any) => {
      this.isLoading = false;
      this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the data');
      console.error('There was an error!', error);
    },
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadSeries();
  }

  onQuotaChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.requestForm.profile_id = Number(selectElement.value);
  }

  onTypeChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.requestForm.type = Number(selectElement.value);
  }


  exportToExcel(): void {
    //this.exportIsLoading = true;
    // add warn
    this.toastService.addToast('warn', 'Wait Exporting Data!', 'Exporting all cards data to excel sheet');
    // create request form to get all data
    const myRequestForm = {...this.requestForm};
    myRequestForm.count = this.tableResponse.total;
    myRequestForm.page = 1;
    // fetch all data
    this.seriesService.getSeries(myRequestForm).subscribe({
      next: (response: any) => {
        this.exportIsLoading = false;
        try {
          // map the data to the format that will be exported
          const data = response.data;
          this.excelService.exportAsExcelFile(data, `allCards`);
        } catch (error) {
          this.toastService.addToast('error', 'Error Message', 'There was an error on exporting the data to excel sheet');
          console.error('There was an error!', error);
        }
      },
      error: (error: any) => {
        this.exportIsLoading = false;
        this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the table data');
        console.error('There was an error!', error);
      },
    });

  }
}
