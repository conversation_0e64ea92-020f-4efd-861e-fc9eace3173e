import { Injectable } from '@angular/core';
import {
  <PERSON>ttpEvent,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ErrorInterceptor implements HttpInterceptor {

  constructor() {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Handle HTTP errors
        if (error.status >= 400 && error.status < 500) {
          // Handle client errors (4xx)
          console.error('Client-side error:', error.error);
          // Perform actions like logging, notifications, etc.
          // Example: this.notificationService.showError('Error occurred:', error.error.message);
        } else if (error.status >= 500) {
          // Handle server errors (5xx)
          console.error('Server-side error:', error.error);
          // Example: this.notificationService.showError('Server error occurred');
        }

        // Throw a new Error or rethrow the original error
        throw new Error(error.message);
      })
    );
  }
}
