<?php

namespace App\Http\Controllers;

use App\Models\SasConnection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Exception;

class SasConnectionController extends Controller
{
    /**
     * حفظ أو تحديث اتصال SAS
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'server_url' => 'required|string|url',
            'username' => 'required|string|min:3|max:50',
            'password' => 'required|string|min:3',
            'connection_name' => 'nullable|string|max:100',
            'shared_secret' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = [
                'connection_name' => $request->input('connection_name', 'SAS Connection'),
                'server_url' => $request->input('server_url'),
                'username' => $request->input('username'),
                'password' => $request->input('password'),
                'shared_secret' => $request->input('shared_secret', 'testing123'),
                'status' => 'testing',
            ];

            // إنشاء أو تحديث الاتصال
            $connection = SasConnection::createOrUpdate($data);

            // اختبار الاتصال
            $testResult = $this->testConnection($connection);

            return response()->json([
                'success' => true,
                'message' => 'تم حفظ إعدادات الاتصال بنجاح',
                'data' => [
                    'connection' => $this->formatConnectionData($connection),
                    'test_result' => $testResult,
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حفظ الإعدادات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * مصادقة المستخدم باستخدام SAS
     */
    public function authenticate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
            'server_url' => 'nullable|string|url',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $username = $request->input('username');
            $password = $request->input('password');
            $serverUrl = $request->input('server_url');

            // البحث عن اتصال موجود أو استخدام البيانات المرسلة
            $connection = null;
            
            if ($serverUrl) {
                $connection = SasConnection::where('server_url', $serverUrl)
                                         ->where('username', $username)
                                         ->first();
                
                if (!$connection) {
                    // إنشاء اتصال جديد
                    $connection = SasConnection::create([
                        'connection_name' => 'Auto-created Connection',
                        'server_url' => $serverUrl,
                        'username' => $username,
                        'password' => $password,
                        'status' => 'testing',
                    ]);
                }
            } else {
                // استخدام آخر اتصال نشط
                $connection = SasConnection::getActiveConnection();
            }

            if (!$connection) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد اتصال SAS مُعد'
                ], 404);
            }

            // التحقق من كلمة المرور
            if ($connection->password !== $password) {
                $connection->recordFailure('كلمة مرور خاطئة');
                return response()->json([
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ], 401);
            }

            // اختبار الاتصال
            $testResult = $this->testConnection($connection);
            
            if ($testResult['success']) {
                $connection->recordSuccess();
                
                // إنشاء token للجلسة
                $token = $this->generateSessionToken($connection, $username);
                
                return response()->json([
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'data' => [
                        'user' => [
                            'id' => $connection->id,
                            'username' => $username,
                            'name' => ucfirst($username),
                            'email' => $username . '@' . parse_url($connection->server_url, PHP_URL_HOST),
                            'role' => $username === 'admin' ? 'administrator' : 'user',
                            'status' => 'active',
                            'permissions' => $username === 'admin' ? ['read', 'write', 'delete', 'admin'] : ['read'],
                        ],
                        'token' => $token,
                        'expires_at' => now()->addHours(24)->toISOString(),
                        'connection' => $this->formatConnectionData($connection),
                    ]
                ]);
            } else {
                $connection->recordFailure($testResult['message']);
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال مع خادم SAS: ' . $testResult['message']
                ], 503);
            }

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في المصادقة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب قائمة الاتصالات
     */
    public function index(): JsonResponse
    {
        try {
            $connections = SasConnection::orderBy('last_successful_at', 'desc')
                                      ->orderBy('created_at', 'desc')
                                      ->get()
                                      ->map(function ($connection) {
                                          return $this->formatConnectionData($connection);
                                      });

            return response()->json([
                'success' => true,
                'data' => $connections
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الاتصالات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار اتصال محدد
     */
    public function test(Request $request, $id = null): JsonResponse
    {
        try {
            if ($id) {
                $connection = SasConnection::findOrFail($id);
            } else {
                // اختبار باستخدام البيانات المرسلة
                $validator = Validator::make($request->all(), [
                    'server_url' => 'required|string|url',
                    'username' => 'required|string',
                    'password' => 'required|string',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'بيانات غير صحيحة',
                        'errors' => $validator->errors()
                    ], 422);
                }

                $connection = new SasConnection([
                    'server_url' => $request->input('server_url'),
                    'username' => $request->input('username'),
                    'password' => $request->input('password'),
                ]);
            }

            $result = $this->testConnection($connection);

            if ($id && $connection->exists) {
                if ($result['success']) {
                    $connection->recordSuccess();
                } else {
                    $connection->recordFailure($result['message']);
                }
            }

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في اختبار الاتصال: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار الاتصال مع الخادم
     */
    private function testConnection(SasConnection $connection): array
    {
        try {
            $url = rtrim($connection->server_url, '/') . '/api/auth/login';
            
            $response = Http::timeout(10)->post($url, [
                'username' => $connection->username,
                'password' => $connection->password,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'تم الاتصال بنجاح',
                    'status_code' => $response->status(),
                    'response_time' => $response->transferStats?->getTransferTime() ?? 0,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل الاتصال - HTTP ' . $response->status(),
                    'status_code' => $response->status(),
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في الاتصال: ' . $e->getMessage(),
                'status_code' => 0,
            ];
        }
    }

    /**
     * تنسيق بيانات الاتصال للعرض
     */
    private function formatConnectionData(SasConnection $connection): array
    {
        return [
            'id' => $connection->id,
            'connection_name' => $connection->connection_name,
            'server_url' => $connection->server_url,
            'server_ip' => $connection->server_ip,
            'server_port' => $connection->server_port,
            'username' => $connection->username,
            'connection_type' => $connection->connection_type,
            'status' => $connection->status,
            'success_rate' => $connection->success_rate,
            'last_test_status' => $connection->last_test_status,
            'last_tested_at' => $connection->last_tested_at?->toISOString(),
            'last_successful_at' => $connection->last_successful_at?->toISOString(),
            'success_count' => $connection->success_count,
            'failure_count' => $connection->failure_count,
            'is_active' => $connection->isActive(),
            'created_at' => $connection->created_at->toISOString(),
        ];
    }

    /**
     * إنشاء token للجلسة
     */
    private function generateSessionToken(SasConnection $connection, string $username): string
    {
        return base64_encode(json_encode([
            'connection_id' => $connection->id,
            'username' => $username,
            'server_url' => $connection->server_url,
            'issued_at' => time(),
            'expires_at' => time() + (24 * 60 * 60), // 24 ساعة
            'hash' => hash('sha256', $connection->id . $username . time()),
        ]));
    }
}
