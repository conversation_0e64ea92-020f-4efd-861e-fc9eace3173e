import { inject, Injectable } from '@angular/core';
import { UserForm, User, UsersData, UserDetails, UserSession, UserTrafficRequestForm } from '../api/users';
import { Observable } from 'rxjs';
import { UserApi } from '../api/users.api';
import { EncryptionService } from '../../common-services/services/encryption.service';
import { TableResponse } from '../../common-services/interfaces/table-response';

@Injectable({
  providedIn: 'root',
})
export class UsersService implements UsersData {
  private userApi = inject(UserApi);
  private encryptionService = inject(EncryptionService);

  getUsers(userForm: UserForm): Observable<TableResponse<User>> {
    const payload = this.encryptionService.generatePayload(userForm);
    return this.userApi.getUsers(payload);
  }

  getOnlineUsers(userForm: UserForm): Observable<TableResponse<User>> {
    const payload = this.encryptionService.generatePayload(userForm);
    return this.userApi.getOnlineUsers(payload);
  }

  getUser(id: string): Observable<UserDetails> {
    return this.userApi.getUser(id);
  }

  getUserSessions(id: string, requestForm: UserForm): Observable<TableResponse<UserSession>> {
    const payload = this.encryptionService.generatePayload(requestForm);
    return  this.userApi.getUserSessions(id, payload);
  }

  getUserTraffic(requestForm: UserTrafficRequestForm): Observable<any> {
    const payload = this.encryptionService.generatePayload(requestForm);
    return this.userApi.getUserTraffic(payload);
  }

  editUser(userId: string | null, userForm: any) {
    const payload = this.encryptionService.generatePayload(userForm);
    return this.userApi.editUser(userId, payload);
  }

  createUser(userForm: any): Observable<any> {
    const payload = this.encryptionService.generatePayload(userForm);
    return this.userApi.createUser(payload);
  }
}
