import { inject, Injectable } from '@angular/core';
import { HttpService } from '../../common-services/services/http.service';
import { EncryptionService } from '../../common-services/services/encryption.service';

@Injectable({
  providedIn: 'root',
})
export class SeriesApi {
  private apiUrl = 'card';
  private httpService = inject(HttpService);
  private encryptionService = inject(EncryptionService);

  getSeries(payload: string) {
    const url = this.apiUrl + '/getAllCards';
    return this.httpService.post(url, { payload });
  }

  getListCardForSeries(id: string, requestForm: any) {
    const payload = this.encryptionService.generatePayload(requestForm);
    const url = `${this.apiUrl}/getListCardForSeries/${id}`;
    return this.httpService.post(url, {payload});
  }

  getCardById(id: string) {
    const url = `${this.apiUrl}/getCardById/${id}`;
    return this.httpService.get(url, {});
  }
}
