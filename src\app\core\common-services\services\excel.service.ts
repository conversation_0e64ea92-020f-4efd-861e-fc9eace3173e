import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class ExcelService {
  constructor() { }

  public exportAsExcelFile(data: any[], fileName: string): void {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Data');

    // Add header row
    const headerRow = worksheet.addRow(Object.keys(data[0]));

    // Add data rows
    data.forEach((item) => {
      worksheet.addRow(Object.values(item));
    });

    // Generate Excel file
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `${fileName}.xlsx`);
    });
  }
}
