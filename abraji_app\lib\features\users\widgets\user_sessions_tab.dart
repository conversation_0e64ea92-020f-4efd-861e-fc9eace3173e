import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';

class UserSessionsTab extends StatefulWidget {
  final String userId;

  const UserSessionsTab({super.key, required this.userId});

  @override
  State<UserSessionsTab> createState() => _UserSessionsTabState();
}

class _UserSessionsTabState extends State<UserSessionsTab> {
  List<UserSession> sessions = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      sessions = _generateMockSessions();
      isLoading = false;
    });
  }

  List<UserSession> _generateMockSessions() {
    return [
      UserSession(
        id: '1',
        startTime: DateTime.now().subtract(const Duration(hours: 2)),
        endTime: DateTime.now().subtract(const Duration(hours: 1)),
        duration: const Duration(hours: 1),
        ipAddress: '*************',
        macAddress: '00:11:22:33:44:55',
        dataUsed: 150.5,
        status: 'completed',
      ),
      UserSession(
        id: '2',
        startTime: DateTime.now().subtract(const Duration(hours: 5)),
        endTime: DateTime.now().subtract(const Duration(hours: 3)),
        duration: const Duration(hours: 2),
        ipAddress: '*************',
        macAddress: '00:11:22:33:44:55',
        dataUsed: 320.8,
        status: 'completed',
      ),
      UserSession(
        id: '3',
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        endTime: null,
        duration: const Duration(minutes: 30),
        ipAddress: '*************',
        macAddress: '00:11:22:33:44:55',
        dataUsed: 45.2,
        status: 'active',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadSessions,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات الجلسات
            _buildSessionStats(),

            const SizedBox(height: 20),

            // قائمة الجلسات
            Text(
              'جلسات المستخدم',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            if (sessions.isEmpty)
              _buildEmptyState()
            else
              ...sessions.map((session) => _buildSessionCard(session)),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionStats() {
    final activeSessions = sessions.where((s) => s.status == 'active').length;
    final totalDataUsed = sessions.fold<double>(
      0,
      (sum, session) => sum + session.dataUsed,
    );
    final totalDuration = sessions.fold<Duration>(
      Duration.zero,
      (sum, session) => sum + session.duration,
    );

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.analytics,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات الجلسات',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الجلسات النشطة',
                    activeSessions.toString(),
                    Icons.wifi,
                    AppColors.success,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الجلسات',
                    sessions.length.toString(),
                    Icons.history,
                    AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'البيانات المستهلكة',
                    '${totalDataUsed.toStringAsFixed(1)} MB',
                    Icons.data_usage,
                    AppColors.secondary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الوقت',
                    _formatDuration(totalDuration),
                    Icons.access_time,
                    AppColors.accent,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSessionCard(UserSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      session.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(session.status),
                    style: AppTypography.bodySmall.copyWith(
                      color: _getStatusColor(session.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                if (session.status == 'active')
                  IconButton(
                    icon: const Icon(Icons.stop_circle, color: Colors.red),
                    onPressed: () => _disconnectSession(session.id),
                    tooltip: 'قطع الاتصال',
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'بدء الجلسة: ${_formatDateTime(session.startTime)}',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            if (session.endTime != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.stop, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Text(
                    'انتهاء الجلسة: ${_formatDateTime(session.endTime!)}',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildSessionInfo(
                    'المدة',
                    _formatDuration(session.duration),
                    Icons.timer,
                  ),
                ),
                Expanded(
                  child: _buildSessionInfo(
                    'البيانات',
                    '${session.dataUsed.toStringAsFixed(1)} MB',
                    Icons.data_usage,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildSessionInfo(
                    'IP',
                    session.ipAddress,
                    Icons.computer,
                  ),
                ),
                Expanded(
                  child: _buildSessionInfo(
                    'MAC',
                    session.macAddress,
                    Icons.device_hub,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionInfo(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: AppTypography.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(Icons.wifi_off, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            'لا توجد جلسات',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يقم المستخدم بتسجيل أي جلسات حتى الآن',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return AppColors.success;
      case 'completed':
        return AppColors.primary;
      case 'disconnected':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'completed':
        return 'مكتمل';
      case 'disconnected':
        return 'منقطع';
      default:
        return status;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '$hoursس $minutesد';
    } else {
      return '$minutesد';
    }
  }

  void _disconnectSession(String sessionId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قطع الاتصال'),
        content: const Text('هل أنت متأكد من قطع اتصال هذه الجلسة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // تنفيذ قطع الاتصال
              setState(() {
                final sessionIndex = sessions.indexWhere(
                  (s) => s.id == sessionId,
                );
                if (sessionIndex != -1) {
                  sessions[sessionIndex] = sessions[sessionIndex].copyWith(
                    status: 'disconnected',
                    endTime: DateTime.now(),
                  );
                }
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('قطع الاتصال'),
          ),
        ],
      ),
    );
  }
}

// نموذج بيانات الجلسة
class UserSession {
  final String id;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration duration;
  final String ipAddress;
  final String macAddress;
  final double dataUsed;
  final String status;

  UserSession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.ipAddress,
    required this.macAddress,
    required this.dataUsed,
    required this.status,
  });

  UserSession copyWith({
    String? id,
    DateTime? startTime,
    DateTime? endTime,
    Duration? duration,
    String? ipAddress,
    String? macAddress,
    double? dataUsed,
    String? status,
  }) {
    return UserSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      ipAddress: ipAddress ?? this.ipAddress,
      macAddress: macAddress ?? this.macAddress,
      dataUsed: dataUsed ?? this.dataUsed,
      status: status ?? this.status,
    );
  }
}
