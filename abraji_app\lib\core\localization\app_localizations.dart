import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('ar', 'SA'), // Arabic
    Locale('en', 'US'), // English
  ];

  // Navigation Labels
  String get dashboard => _localizedValues[locale.languageCode]!['dashboard']!;
  String get transactions => _localizedValues[locale.languageCode]!['transactions']!;
  String get wallet => _localizedValues[locale.languageCode]!['wallet']!;
  String get settings => _localizedValues[locale.languageCode]!['settings']!;

  // Common
  String get language => _localizedValues[locale.languageCode]!['language']!;
  String get arabic => _localizedValues[locale.languageCode]!['arabic']!;
  String get english => _localizedValues[locale.languageCode]!['english']!;
  String get save => _localizedValues[locale.languageCode]!['save']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;

  // SAS Connection
  String get sasConnection => _localizedValues[locale.languageCode]!['sas_connection']!;
  String get connected => _localizedValues[locale.languageCode]!['connected']!;
  String get disconnected => _localizedValues[locale.languageCode]!['disconnected']!;

  // Settings
  String get generalSettings => _localizedValues[locale.languageCode]!['general_settings']!;
  String get appearance => _localizedValues[locale.languageCode]!['appearance']!;
  String get notifications => _localizedValues[locale.languageCode]!['notifications']!;
  String get about => _localizedValues[locale.languageCode]!['about']!;

  static const Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      // Navigation
      'dashboard': 'الرئيسية',
      'transactions': 'المعاملات',
      'wallet': 'المحفظة',
      'settings': 'الإعدادات',
      
      // Common
      'language': 'اللغة',
      'arabic': 'العربية',
      'english': 'الإنجليزية',
      'save': 'حفظ',
      'cancel': 'إلغاء',
      
      // SAS Connection
      'sas_connection': 'ربط SAS Radius',
      'connected': 'متصل',
      'disconnected': 'غير متصل',
      
      // Settings
      'general_settings': 'الإعدادات العامة',
      'appearance': 'المظهر',
      'notifications': 'الإشعارات',
      'about': 'حول التطبيق',
    },
    'en': {
      // Navigation
      'dashboard': 'Dashboard',
      'transactions': 'Transactions',
      'wallet': 'Wallet',
      'settings': 'Settings',
      
      // Common
      'language': 'Language',
      'arabic': 'Arabic',
      'english': 'English',
      'save': 'Save',
      'cancel': 'Cancel',
      
      // SAS Connection
      'sas_connection': 'SAS Radius Connection',
      'connected': 'Connected',
      'disconnected': 'Disconnected',
      
      // Settings
      'general_settings': 'General Settings',
      'appearance': 'Appearance',
      'notifications': 'Notifications',
      'about': 'About',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
