APP_NAME=AbrajiAPIs
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# SAS Radius Configuration
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost

# SAS4 Dynamic Access Configuration
SAS4_ENCRYPTION_METHOD=AES-256-CBC
SAS4_ENCRYPTION_KEY=your-32-character-secret-key-here
SAS4_ENCRYPTION_IV=your-16-char-iv
SAS4_CONNECTION_TIMEOUT=30
SAS4_READ_TIMEOUT=60
SAS4_MAX_RETRIES=3
SAS4_USER_AGENT="AbrajiAPIs-SAS4-Client/1.0"
SAS4_ALLOWED_IPS=""
SAS4_REQUIRE_HTTPS=false
SAS4_VERIFY_SSL=true
SAS4_LOGGING_ENABLED=true
SAS4_LOG_REQUESTS=true
SAS4_LOG_RESPONSES=false
SAS4_LOG_ERRORS=true
SAS4_CACHE_ENABLED=true
SAS4_CACHE_TTL=300
SAS4_CACHE_PREFIX=sas4_
SAS4_TEST_IP=*************
SAS4_TEST_USERNAME=admin
SAS4_TEST_PASSWORD=admin123
SAS4_TEST_ENDPOINT=dashboard
