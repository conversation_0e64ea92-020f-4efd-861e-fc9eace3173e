import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../../../core/config/vps_config.dart';

/// خدمة اختبار شاملة للـ VPS
class VpsTestService {
  static const Duration timeout = Duration(seconds: 15);

  /// اختبار الاتصال الأساسي مع VPS
  static Future<VpsTestResult> testBasicConnection() async {
    final results = <String, bool>{};
    final errors = <String, String>{};

    // اختبار URLs مختلفة
    for (final url in VpsConfig.testUrls) {
      try {
        final response = await http.get(Uri.parse(url)).timeout(timeout);

        results[url] = response.statusCode < 500;
        if (response.statusCode >= 500) {
          errors[url] = 'Server Error: ${response.statusCode}';
        }
      } catch (e) {
        results[url] = false;
        errors[url] = e.toString();
      }
    }

    final successCount = results.values.where((success) => success).length;
    final totalCount = results.length;

    return VpsTestResult(
      success: successCount > 0,
      message: '$successCount/$totalCount URLs accessible',
      details: results,
      errors: errors,
    );
  }

  /// اختبار endpoints المصادقة
  static Future<VpsTestResult> testAuthEndpoints({
    required String baseUrl,
    required String username,
    required String password,
  }) async {
    final results = <String, bool>{};
    final errors = <String, String>{};

    final authEndpoints = [
      '/api/auth/login',
      '/admin/api/index.php/api/login',
      '/api/login',
      '/login',
    ];

    for (final endpoint in authEndpoints) {
      try {
        final url = Uri.parse('$baseUrl$endpoint');

        // تحضير البيانات حسب نوع الـ endpoint
        Map<String, dynamic> requestBody;
        if (endpoint.contains('admin/api/index.php')) {
          final credentials = base64Encode(utf8.encode('$username:$password'));
          requestBody = {'payload': credentials};
        } else {
          requestBody = {'username': username, 'password': password};
        }

        final response = await http
            .post(
              url,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
              body: json.encode(requestBody),
            )
            .timeout(timeout);

        // اعتبار 200, 401, 422 كاستجابات صالحة (الخادم يعمل)
        final isValid = [200, 401, 422].contains(response.statusCode);
        results[endpoint] = isValid;

        if (!isValid) {
          errors[endpoint] = 'HTTP ${response.statusCode}';
        }
      } catch (e) {
        results[endpoint] = false;
        errors[endpoint] = e.toString();
      }
    }

    final successCount = results.values.where((success) => success).length;
    final totalCount = results.length;

    return VpsTestResult(
      success: successCount > 0,
      message: '$successCount/$totalCount auth endpoints responding',
      details: results,
      errors: errors,
    );
  }

  /// اختبار endpoints المستخدمين
  static Future<VpsTestResult> testUsersEndpoints({
    required String baseUrl,
    String? authToken,
  }) async {
    final results = <String, bool>{};
    final errors = <String, String>{};

    final usersEndpoints = [
      '/api/users/table',
      '/api/users/online',
      '/api/users',
      '/api/subscribers',
    ];

    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }

    for (final endpoint in usersEndpoints) {
      try {
        final url = Uri.parse('$baseUrl$endpoint');

        final response = await http
            .post(
              url,
              headers: headers,
              body: json.encode({'page': 1, 'limit': 10}),
            )
            .timeout(timeout);

        // اعتبار 200, 401, 403, 422 كاستجابات صالحة (الخادم يعمل)
        final isValid = [200, 401, 403, 422].contains(response.statusCode);
        results[endpoint] = isValid;

        if (!isValid) {
          errors[endpoint] = 'HTTP ${response.statusCode}';
        }
      } catch (e) {
        results[endpoint] = false;
        errors[endpoint] = e.toString();
      }
    }

    final successCount = results.values.where((success) => success).length;
    final totalCount = results.length;

    return VpsTestResult(
      success: successCount > 0,
      message: '$successCount/$totalCount users endpoints responding',
      details: results,
      errors: errors,
    );
  }

  /// اختبار شامل للـ VPS
  static Future<VpsComprehensiveTest> runComprehensiveTest({
    required String baseUrl,
    required String username,
    required String password,
  }) async {
    debugPrint('🔍 بدء الاختبار الشامل للـ VPS...');

    // 1. اختبار الاتصال الأساسي
    final basicTest = await testBasicConnection();
    debugPrint('✅ اختبار الاتصال الأساسي: ${basicTest.success}');

    // 2. اختبار endpoints المصادقة
    final authTest = await testAuthEndpoints(
      baseUrl: baseUrl,
      username: username,
      password: password,
    );
    debugPrint('✅ اختبار endpoints المصادقة: ${authTest.success}');

    // 3. اختبار endpoints المستخدمين
    final usersTest = await testUsersEndpoints(baseUrl: baseUrl);
    debugPrint('✅ اختبار endpoints المستخدمين: ${usersTest.success}');

    // 4. تحليل النتائج
    final overallSuccess =
        basicTest.success && (authTest.success || usersTest.success);

    String recommendation = '';
    if (overallSuccess) {
      recommendation = 'VPS يعمل بشكل جيد. يمكن المتابعة مع التطبيق.';
    } else if (basicTest.success) {
      recommendation = 'VPS متاح لكن APIs غير مُعدة. تحقق من إعداد Laravel.';
    } else {
      recommendation = 'VPS غير متاح. تحقق من IP والشبكة.';
    }

    return VpsComprehensiveTest(
      overallSuccess: overallSuccess,
      basicConnection: basicTest,
      authEndpoints: authTest,
      usersEndpoints: usersTest,
      recommendation: recommendation,
      testedAt: DateTime.now(),
    );
  }
}

/// نتيجة اختبار VPS
class VpsTestResult {
  final bool success;
  final String message;
  final Map<String, bool> details;
  final Map<String, String> errors;

  VpsTestResult({
    required this.success,
    required this.message,
    required this.details,
    required this.errors,
  });

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('Result: ${success ? "✅ Success" : "❌ Failed"}');
    buffer.writeln('Message: $message');

    if (details.isNotEmpty) {
      buffer.writeln('\nDetails:');
      details.forEach((key, value) {
        buffer.writeln('  ${value ? "✅" : "❌"} $key');
      });
    }

    if (errors.isNotEmpty) {
      buffer.writeln('\nErrors:');
      errors.forEach((key, error) {
        buffer.writeln('  ❌ $key: $error');
      });
    }

    return buffer.toString();
  }
}

/// اختبار شامل للـ VPS
class VpsComprehensiveTest {
  final bool overallSuccess;
  final VpsTestResult basicConnection;
  final VpsTestResult authEndpoints;
  final VpsTestResult usersEndpoints;
  final String recommendation;
  final DateTime testedAt;

  VpsComprehensiveTest({
    required this.overallSuccess,
    required this.basicConnection,
    required this.authEndpoints,
    required this.usersEndpoints,
    required this.recommendation,
    required this.testedAt,
  });

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('🔍 VPS Comprehensive Test Results');
    buffer.writeln('Tested at: ${testedAt.toLocal()}');
    buffer.writeln('Overall: ${overallSuccess ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln('\n📡 Basic Connection:');
    buffer.writeln(
      basicConnection
          .toString()
          .split('\n')
          .map((line) => '  $line')
          .join('\n'),
    );
    buffer.writeln('\n🔐 Auth Endpoints:');
    buffer.writeln(
      authEndpoints.toString().split('\n').map((line) => '  $line').join('\n'),
    );
    buffer.writeln('\n👥 Users Endpoints:');
    buffer.writeln(
      usersEndpoints.toString().split('\n').map((line) => '  $line').join('\n'),
    );
    buffer.writeln('\n💡 Recommendation:');
    buffer.writeln('  $recommendation');

    return buffer.toString();
  }
}
