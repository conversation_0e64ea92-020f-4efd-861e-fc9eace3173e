import 'package:flutter/material.dart';
import '../../../core/services/sas_api_service.dart';
import '../../../core/config/api_config.dart';
import '../../../core/config/vps_config.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../services/vps_test_service.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({super.key});

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  final _urlController = TextEditingController(text: VpsConfig.vpsUrl);
  final _usernameController = TextEditingController(text: 'admin');
  final _passwordController = TextEditingController(text: 'admin123');

  bool _isLoading = false;
  String _result = '';
  SasApiService? _apiService;

  @override
  void dispose() {
    _urlController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _testConnection() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار الاتصال...';
    });

    try {
      _apiService = SasApiService(baseUrl: _urlController.text);
      final success = await _apiService!.testConnection();

      if (!mounted) return;
      setState(() {
        _result = success
            ? '✅ نجح الاتصال مع الخادم'
            : '❌ فشل الاتصال مع الخادم';
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _result = '❌ خطأ في الاتصال: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testAuthentication() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار المصادقة...';
    });

    try {
      _apiService = SasApiService(baseUrl: _urlController.text);
      final response = await _apiService!.authenticate(
        username: _usernameController.text,
        password: _passwordController.text,
      );

      if (!mounted) return;
      setState(() {
        if (response.success) {
          _result =
              '✅ نجحت المصادقة مع ${_urlController.text}/api/auth/login\n\n'
              '👤 معلومات المستخدم:\n'
              '• الاسم: ${response.user?.name}\n'
              '• اسم المستخدم: ${response.user?.username}\n'
              '• البريد: ${response.user?.email}\n'
              '• الدور: ${response.user?.role}\n'
              '• الحالة: ${response.user?.status}\n'
              '• الصلاحيات: ${response.user?.permissions.join(', ')}\n\n'
              '🔑 معلومات الجلسة:\n'
              '• Token: ${response.token != null ? "✅ متوفر" : "❌ غير متوفر"}\n'
              '• انتهاء الصلاحية: ${response.expiresAt?.toLocal().toString() ?? "غير محدد"}';

          // حفظ token للاختبارات التالية
          if (response.token != null) {
            _apiService!.setAuthToken(response.token!);
          }
        } else {
          _result =
              '❌ فشلت المصادقة مع ${_urlController.text}/api/auth/login\n\n'
              'السبب: ${response.message}\n\n'
              '💡 تأكد من:\n'
              '• صحة اسم المستخدم وكلمة المرور\n'
              '• أن Laravel API يعمل بشكل صحيح\n'
              '• أن قاعدة البيانات تحتوي على المستخدم';
        }
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _result = '❌ خطأ في المصادقة: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testGetUsers() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = 'جاري جلب المستخدمين...';
    });

    try {
      _apiService ??= SasApiService(baseUrl: _urlController.text);

      final response = await _apiService!.getUsers();

      if (!mounted) return;
      setState(() {
        if (response.success) {
          final usersInfo = response.users
              .map(
                (user) => '• ${user.name} (${user.username}) - ${user.status}',
              )
              .join('\n');

          _result = '✅ تم جلب ${response.users.length} مستخدم\n\n$usersInfo';
        } else {
          _result = '❌ فشل جلب المستخدمين: ${response.message}';
        }
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _result = '❌ خطأ في جلب المستخدمين: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _runComprehensiveTest() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result =
          'جاري تشغيل الاختبار الشامل للـ VPS...\n\n'
          '🔍 اختبار الاتصال الأساسي...\n'
          '🔐 اختبار endpoints المصادقة...\n'
          '👥 اختبار endpoints المستخدمين...\n';
    });

    try {
      final comprehensiveTest = await VpsTestService.runComprehensiveTest(
        baseUrl: _urlController.text,
        username: _usernameController.text,
        password: _passwordController.text,
      );

      if (!mounted) return;
      setState(() {
        _result = comprehensiveTest.toString();
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _result = '❌ خطأ في الاختبار الشامل: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$label: $value',
        style: AppTypography.bodySmall.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار API'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _result = '';
              });
            },
            tooltip: 'مسح النتائج',
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // URL Input
              TextField(
                controller: _urlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الخادم',
                  hintText: 'https://yourdomain.com',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 16),

              // Username Input
              TextField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المستخدم',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 16),

              // Password Input
              TextField(
                controller: _passwordController,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
              ),

              const SizedBox(height: 24),

              // Test Buttons
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : _testConnection,
                          icon: const Icon(Icons.wifi, size: 18),
                          label: const Text('اختبار الاتصال'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : _testAuthentication,
                          icon: const Icon(Icons.login, size: 18),
                          label: const Text('اختبار المصادقة'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testGetUsers,
                      icon: const Icon(Icons.people, size: 18),
                      label: const Text('اختبار جلب المستخدمين'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _runComprehensiveTest,
                      icon: const Icon(Icons.analytics, size: 18),
                      label: const Text('اختبار شامل للـ VPS'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.info,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Results
              Container(
                constraints: BoxConstraints(
                  minHeight: 200,
                  maxHeight: MediaQuery.of(context).size.height * 0.4,
                ),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: _isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('جاري التحميل...'),
                          ],
                        ),
                      )
                    : SingleChildScrollView(
                        child: SelectableText(
                          _result.isEmpty ? 'النتائج ستظهر هنا...' : _result,
                          style: AppTypography.bodySmall.copyWith(
                            fontFamily: 'monospace',
                            height: 1.4,
                          ),
                        ),
                      ),
              ),

              const SizedBox(height: 16),

              // Environment Info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات البيئة:',
                      style: AppTypography.titleSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildInfoChip(
                          'البيئة',
                          ApiConfig.isProduction ? 'إنتاج' : 'تطوير',
                        ),
                        _buildInfoChip('VPS IP', VpsConfig.vpsIp),
                        _buildInfoChip(
                          'المهلة الزمنية',
                          '${ApiConfig.defaultTimeout.inSeconds}s',
                        ),
                        _buildInfoChip(
                          'URLs للاختبار',
                          '${VpsConfig.testUrls.length}',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
