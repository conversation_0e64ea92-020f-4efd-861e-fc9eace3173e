import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../core/theme/app_colors.dart';

class SasMagicIcon extends StatefulWidget {
  final double size;
  final bool isConnected;
  final Color? primaryColor;
  final Color? secondaryColor;
  final bool enableAnimation;

  const SasMagicIcon({
    super.key,
    this.size = 32,
    this.isConnected = false,
    this.primaryColor,
    this.secondaryColor,
    this.enableAnimation = true,
  });

  @override
  State<SasMagicIcon> createState() => _SasMagicIconState();
}

class _SasMagicIconState extends State<SasMagicIcon>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _sparkleController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _sparkleAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد انيميشن الدوران
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    // إعداد انيميشن النبضة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // إعداد انيميشن البريق
    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _sparkleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeInOut),
    );

    if (widget.enableAnimation) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _sparkleController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(SasMagicIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.enableAnimation != oldWidget.enableAnimation) {
      if (widget.enableAnimation) {
        _startAnimations();
      } else {
        _rotationController.stop();
        _pulseController.stop();
        _sparkleController.stop();
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor =
        widget.primaryColor ??
        (widget.isConnected ? AppColors.success : AppColors.primary);
    final secondaryColor =
        widget.secondaryColor ??
        (widget.isConnected
            ? AppColors.success.withValues(alpha: 0.3)
            : AppColors.primary.withValues(alpha: 0.3));

    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _pulseAnimation,
        _sparkleAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableAnimation ? _pulseAnimation.value : 1.0,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: CustomPaint(
              painter: SasMagicPainter(
                rotationAngle: widget.enableAnimation
                    ? _rotationAnimation.value
                    : 0,
                sparkleProgress: widget.enableAnimation
                    ? _sparkleAnimation.value
                    : 0,
                primaryColor: primaryColor,
                secondaryColor: secondaryColor,
                isConnected: widget.isConnected,
              ),
            ),
          ),
        );
      },
    );
  }
}

class SasMagicPainter extends CustomPainter {
  final double rotationAngle;
  final double sparkleProgress;
  final Color primaryColor;
  final Color secondaryColor;
  final bool isConnected;

  SasMagicPainter({
    required this.rotationAngle,
    required this.sparkleProgress,
    required this.primaryColor,
    required this.secondaryColor,
    required this.isConnected,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // رسم الخلفية السحرية
    _drawMagicBackground(canvas, center, radius);

    // رسم الدوائر المتحركة
    _drawRotatingCircles(canvas, center, radius);

    // رسم أيقونة SAS الرئيسية
    _drawSasIcon(canvas, center, radius);

    // رسم البريق السحري
    _drawSparkles(canvas, center, radius);

    // رسم مؤشر الاتصال
    _drawConnectionIndicator(canvas, center, radius);
  }

  void _drawMagicBackground(Canvas canvas, Offset center, double radius) {
    // رسم تدرج دائري سحري
    final gradient = RadialGradient(
      colors: [
        secondaryColor.withValues(alpha: 0.8),
        secondaryColor.withValues(alpha: 0.4),
        secondaryColor.withValues(alpha: 0.1),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.7, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(
        Rect.fromCircle(center: center, radius: radius),
      );

    canvas.drawCircle(center, radius, paint);
  }

  void _drawRotatingCircles(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = primaryColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // رسم دوائر متحركة متعددة
    for (int i = 0; i < 3; i++) {
      final circleRadius = radius * (0.3 + i * 0.2);
      final rotationOffset = rotationAngle + (i * math.pi / 3);

      canvas.save();
      canvas.translate(center.dx, center.dy);
      canvas.rotate(rotationOffset);

      // رسم دائرة منقطة
      _drawDottedCircle(canvas, Offset.zero, circleRadius, paint);

      canvas.restore();
    }
  }

  void _drawDottedCircle(
    Canvas canvas,
    Offset center,
    double radius,
    Paint paint,
  ) {
    const dotCount = 12;
    const dotSize = 3.0;

    for (int i = 0; i < dotCount; i++) {
      final angle = (i * 2 * math.pi) / dotCount;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      canvas.drawCircle(Offset(x, y), dotSize, paint);
    }
  }

  void _drawSasIcon(Canvas canvas, Offset center, double radius) {
    // رسم خلفية دائرية للنص مع تدرج جمالي
    final backgroundGradient = RadialGradient(
      colors: isConnected
          ? [
              // تدرج أخضر جميل عند الاتصال
              const Color(0xFF4CAF50), // أخضر فاتح
              const Color(0xFF2E7D32), // أخضر داكن
              const Color(0xFF1B5E20), // أخضر أكثر قتامة
            ]
          : [
              // تدرج أزرق رمادي عند عدم الاتصال
              const Color(0xFF607D8B), // أزرق رمادي
              const Color(0xFF455A64), // أزرق رمادي داكن
              const Color(0xFF263238), // أزرق رمادي أكثر قتامة
            ],
      stops: const [0.0, 0.6, 1.0],
    );

    final backgroundPaint = Paint()
      ..shader = backgroundGradient.createShader(
        Rect.fromCircle(center: center, radius: radius * 0.6),
      )
      ..style = PaintingStyle.fill;

    // رسم دائرة خلفية مع تأثير توهج
    canvas.drawCircle(center, radius * 0.6, backgroundPaint);

    // رسم حدود متوهجة مع ألوان محسنة
    final borderColor = isConnected
        ? const Color(0xFF81C784) // أخضر فاتح متوهج
        : const Color(0xFF90A4AE); // رمادي أزرق فاتح

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(center, radius * 0.6, borderPaint);

    // رسم نص SAS مع ألوان جمالية متناسقة
    final textColor = Colors.white; // أبيض دائماً للوضوح

    final textStyle = TextStyle(
      color: textColor,
      fontSize: radius * 0.35,
      fontWeight: FontWeight.w900, // أكثر سماكة
      letterSpacing: 2.0, // مسافة أكبر بين الحروف
      shadows: [
        // ظل أساسي قوي
        Shadow(
          color: Colors.black.withValues(alpha: 0.6),
          offset: const Offset(1, 1),
          blurRadius: 3,
        ),
        // ظل توهج ملون حسب الحالة
        Shadow(
          color: isConnected
              ? const Color(0xFF4CAF50).withValues(alpha: 0.8) // توهج أخضر
              : const Color(0xFFF44336).withValues(alpha: 0.8), // توهج أحمر
          offset: const Offset(0, 0),
          blurRadius: 10,
        ),
        // ظل خارجي للعمق
        Shadow(
          color: Colors.black.withValues(alpha: 0.3),
          offset: const Offset(2, 2),
          blurRadius: 8,
        ),
        // ظل داخلي للتباين
        Shadow(
          color: isConnected
              ? const Color(0xFF2E7D32).withValues(alpha: 0.5)
              : const Color(0xFFD32F2F).withValues(alpha: 0.5),
          offset: const Offset(-1, -1),
          blurRadius: 2,
        ),
      ],
    );

    final textSpan = TextSpan(text: 'SAS', style: textStyle);

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    // توسيط النص
    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );

    textPainter.paint(canvas, textOffset);

    // رسم نقاط الاتصال حول النص مع تأثيرات جمالية
    if (isConnected) {
      // نقاط متوهجة خضراء عند الاتصال
      final dotPaint = Paint()
        ..color = const Color(0xFF4CAF50)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

      for (int i = 0; i < 8; i++) {
        final angle = (i * 2 * math.pi) / 8 + rotationAngle;
        final dotRadius = radius * 0.75;
        final x = center.dx + dotRadius * math.cos(angle);
        final y = center.dy + dotRadius * math.sin(angle);

        // نقطة متوهجة خضراء
        canvas.drawCircle(Offset(x, y), 3, dotPaint);

        // نقطة داخلية بيضاء للتباين
        final innerDotPaint = Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;
        canvas.drawCircle(Offset(x, y), 1.2, innerDotPaint);
      }
    } else {
      // نقاط حمراء خافتة عند عدم الاتصال
      final dotPaint = Paint()
        ..color = const Color(0xFFF44336).withValues(alpha: 0.7)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

      for (int i = 0; i < 6; i++) {
        final angle = (i * 2 * math.pi) / 6;
        final dotRadius = radius * 0.75;
        final x = center.dx + dotRadius * math.cos(angle);
        final y = center.dy + dotRadius * math.sin(angle);

        // نقطة حمراء
        canvas.drawCircle(Offset(x, y), 2, dotPaint);

        // نقطة داخلية بيضاء خافتة
        final innerDotPaint = Paint()
          ..color = Colors.white.withValues(alpha: 0.8)
          ..style = PaintingStyle.fill;
        canvas.drawCircle(Offset(x, y), 0.8, innerDotPaint);
      }
    }
  }

  void _drawSparkles(Canvas canvas, Offset center, double radius) {
    final sparklePaint = Paint()
      ..color = primaryColor.withValues(alpha: sparkleProgress * 0.8)
      ..style = PaintingStyle.fill;

    // رسم نجوم براقة حول الأيقونة
    for (int i = 0; i < 8; i++) {
      final angle = (i * 2 * math.pi) / 8 + rotationAngle * 0.5;
      final distance =
          radius * (0.7 + 0.2 * math.sin(sparkleProgress * 2 * math.pi));
      final x = center.dx + distance * math.cos(angle);
      final y = center.dy + distance * math.sin(angle);

      _drawStar(canvas, Offset(x, y), radius * 0.05, sparklePaint);
    }
  }

  void _drawStar(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    const points = 4;

    for (int i = 0; i < points * 2; i++) {
      final angle = (i * math.pi) / points;
      final radius = (i % 2 == 0) ? size : size * 0.5;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  void _drawConnectionIndicator(Canvas canvas, Offset center, double radius) {
    if (!isConnected) return;

    // رسم هالة خضراء للاتصال
    final glowPaint = Paint()
      ..color = AppColors.success.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);

    canvas.drawCircle(center, radius * 0.9, glowPaint);

    // رسم نقاط اتصال متحركة
    for (int i = 0; i < 3; i++) {
      final angle = rotationAngle + (i * 2 * math.pi / 3);
      final x = center.dx + (radius * 0.85) * math.cos(angle);
      final y = center.dy + (radius * 0.85) * math.sin(angle);

      final dotPaint = Paint()
        ..color = AppColors.success
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(x, y), 2, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant SasMagicPainter oldDelegate) {
    return oldDelegate.rotationAngle != rotationAngle ||
        oldDelegate.sparkleProgress != sparkleProgress ||
        oldDelegate.isConnected != isConnected;
  }
}
