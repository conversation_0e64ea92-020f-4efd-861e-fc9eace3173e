import { ColumnState, RequestForm } from "../../common-services/interfaces/table-response";


export interface Debt {
  id: number;
  user_id: number;
  amount: number;
  amount_paid: number;
  description: string;
  debt_timestamp: string;
  pay: number;
  paid_at: string;
  created_at: string;
  updated_at: string;
  username: string;
}

export interface DebtStatistics {
  number_of_debts_done: number;
  number_of_debts_not_done: number;
  amount_of_money_paid: number;
  amount_of_money_not_paid: number;
}

export interface DebtHistory {
  id: number;
  debt_id: number;
  amount: number;
  paid_at: string;
  created_at: string;
  updated_at: string;
}

export interface DebtForm extends RequestForm {
  pay: boolean | null;
}

export const initialDeptColumnsState: ColumnState[] = [
  { key: 'username', hidden: false },
  { key: 'amount', hidden: false },
  { key: 'amount_paid', hidden: false },
  { key: 'description', hidden: false },
  { key: 'debt_timestamp', hidden: false },
  { key: 'pay', hidden: false },
  { key: 'paid_at', hidden: false },
  { key: 'created_at', hidden: false },
];

