import { Component, inject } from '@angular/core';
import { TranslationService } from '../../../core/common-services/services/translation.service';
import { AuthService } from '../../../core/auth-services/services/auth.service';

@Component({
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.scss'],
})
export class SideBarComponent {
  private tranService = inject(TranslationService);
  private authService = inject(AuthService);

  changeLang(lang: string) {
    this.tranService.setLanguage(lang);
  }
  logout(): void {
    this.authService.logout();
  }
}
