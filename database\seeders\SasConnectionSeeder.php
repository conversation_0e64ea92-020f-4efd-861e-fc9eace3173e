<?php

namespace Database\Seeders;

use App\Models\SasConnection;
use Illuminate\Database\Seeder;

class SasConnectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء اتصالات تجريبية
        $connections = [
            [
                'connection_name' => 'Local Test Connection',
                'server_url' => 'http://localhost:8000',
                'username' => 'admin',
                'password' => 'admin123',
                'status' => 'active',
                'success_count' => 5,
                'failure_count' => 1,
                'last_successful_at' => now()->subHours(2),
                'last_tested_at' => now()->subHours(2),
            ],
            [
                'connection_name' => 'VPS Production Connection',
                'server_url' => 'http://*************',
                'username' => 'admin',
                'password' => 'admin123',
                'status' => 'active',
                'success_count' => 10,
                'failure_count' => 0,
                'last_successful_at' => now()->subMinutes(30),
                'last_tested_at' => now()->subMinutes(30),
            ],
            [
                'connection_name' => 'Test User Connection',
                'server_url' => 'http://*************',
                'username' => 'test',
                'password' => 'test123',
                'status' => 'testing',
                'success_count' => 2,
                'failure_count' => 3,
                'last_tested_at' => now()->subHours(1),
            ],
            [
                'connection_name' => 'Demo Connection',
                'server_url' => 'http://*************',
                'username' => 'demo',
                'password' => 'demo123',
                'status' => 'inactive',
                'success_count' => 0,
                'failure_count' => 5,
                'last_tested_at' => now()->subDays(1),
                'last_error' => 'Connection timeout',
            ],
        ];

        foreach ($connections as $connectionData) {
            SasConnection::updateOrCreate(
                [
                    'server_url' => $connectionData['server_url'],
                    'username' => $connectionData['username'],
                ],
                $connectionData
            );
        }

        $this->command->info('تم إنشاء ' . count($connections) . ' اتصال SAS تجريبي');
    }
}
