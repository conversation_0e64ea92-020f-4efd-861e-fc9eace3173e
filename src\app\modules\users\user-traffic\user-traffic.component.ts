import { Component, inject, OnInit } from '@angular/core';
import { Chart } from 'angular-highcharts';
import { UserTrafficData, UserTrafficRequestForm } from '../../../core/user-services/api/users';
import { ActivatedRoute } from '@angular/router';
import { UsersService } from '../../../core/user-services/services/users.service';

@Component({
  selector: 'app-user-traffic',
  templateUrl: './user-traffic.component.html',
  styleUrls: ['./user-traffic.component.scss']
})
export class UserTrafficComponent implements OnInit {
  private userId: string | null = null;
  protected userService = inject(UsersService);
  private route = inject(ActivatedRoute);
  userTrafficData!: UserTrafficData;
  chart = new Chart();
  isLoading = false;
  requestForm!: UserTrafficRequestForm;
  tableColumns: (keyof UserTrafficData)[] = ['rx', 'tx', 'total', 'total_real', 'free_traffic'];

  // Define arrays for dropdown options
  months = [
    { value: 1, name: 'Jan' },
    { value: 2, name: 'Feb' },
    { value: 3, name: '<PERSON>' },
    { value: 4, name: 'Apr' },
    { value: 5, name: 'May' },
    { value: 6, name: 'Jun' },
    { value: 7, name: 'Jul' },
    { value: 8, name: 'Aug' },
    { value: 9, name: 'Sep' },
    { value: 10, name: 'Oct' },
    { value: 11, name: 'Nov' },
    { value: 12, name: 'Dec' }
  ];
  years = [2024, 2023, 2022, 2021, 2020, 2019, 2018];
  getDays(): string[] {
    return this.userTrafficData.rx.map((_, index) => `Day ${index + 1}`);
  }
  getColumnData(column: keyof UserTrafficData, index: number): number {
    return this.userTrafficData[column][index];
  }

  refreshData() {
    // Implement logic to fetch data based on the form values
    console.log('Form values:', this.requestForm);
    // Call your data loading function here based on form values
    this.loadTrafficData();
  }


  ngOnInit() {
    // Access parent route to get the id parameter
    this.route.parent?.paramMap.subscribe(params => {
    this.userId = params.get('id');

    // get current year and month
    const date = new Date();
    const currentYear = date.getFullYear();
    const currentMonth = date.getMonth() + 1;

    // prepare the request form
    this.requestForm = {
      report_type: 'daily',
      month: currentMonth,
      year: currentYear,
      user_id: this.userId || '',
    };

    if (this.userId) {
      this.loadTrafficData();
    }
  });
  }

  loadTrafficData() {
    this.isLoading = true;
    this.userService.getUserTraffic(this.requestForm).subscribe({
      next: (response: any) => {
        this.userTrafficData = response.data;
        console.log('User traffic data:', response);
      },
      complete: () => {
        this.isLoading = false;
        // Create the chart
        this.createChart();
    },
      error: (error: any) => {
        this.isLoading = false;
        console.error('Error loading user traffic data:', error);
      }
    });
  }

  // Create the chart
  createChart(): void {
    const categories = this.userTrafficData.rx.map((_, index) => `${index + 1}`);
    this.chart = new Chart({
      chart: {
        type: 'line',
      },
      title: {
        text: 'User Traffic'
      },
      xAxis: {
        categories: categories
      },
      yAxis: {
        title: {
          text: 'Traffic',
        }
      },
      series: [
        { name: 'Download', type: 'line', data: this.userTrafficData.rx },
        { name: 'Upload',type: 'line' , data: this.userTrafficData.tx },
        { name: 'Total Real', type: 'line', data: this.userTrafficData.total_real },
        { name: 'Total', type: 'line', data: this.userTrafficData.total}
      ],
      colors: ['#edb244', '#9bed53', '#9baaaa', '#6c3ddd']
    });
  }

}
