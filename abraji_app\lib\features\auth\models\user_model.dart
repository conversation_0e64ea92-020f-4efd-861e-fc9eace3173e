class User {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final List<String>? roles;
  final Map<String, dynamic>? preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    this.createdAt,
    this.updatedAt,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.roles,
    this.preferences,
  });

  // Factory constructor from JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      avatar: json['avatar'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      isEmailVerified: json['email_verified'] ?? false,
      isPhoneVerified: json['phone_verified'] ?? false,
      roles: json['roles'] != null 
          ? List<String>.from(json['roles']) 
          : null,
      preferences: json['preferences'] != null 
          ? Map<String, dynamic>.from(json['preferences']) 
          : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'email_verified': isEmailVerified,
      'phone_verified': isPhoneVerified,
      'roles': roles,
      'preferences': preferences,
    };
  }

  // Copy with method for immutable updates
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    List<String>? roles,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      roles: roles ?? this.roles,
      preferences: preferences ?? this.preferences,
    );
  }

  // Get display name
  String get displayName {
    if (name.isNotEmpty) return name;
    if (email.isNotEmpty) return email.split('@').first;
    return 'مستخدم';
  }

  // Get initials for avatar
  String get initials {
    if (name.isEmpty) return 'م';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'.toUpperCase();
    }
  }

  // Check if user has specific role
  bool hasRole(String role) {
    return roles?.contains(role) ?? false;
  }

  // Check if user is admin
  bool get isAdmin => hasRole('admin');

  // Check if user is verified
  bool get isVerified => isEmailVerified;

  // Get user preference
  T? getPreference<T>(String key) {
    return preferences?[key] as T?;
  }

  // Set user preference (returns new User instance)
  User setPreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences ?? {});
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  // Remove user preference (returns new User instance)
  User removePreference(String key) {
    final newPreferences = Map<String, dynamic>.from(preferences ?? {});
    newPreferences.remove(key);
    return copyWith(preferences: newPreferences);
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// User Statistics Model
class UserStats {
  final int totalTransactions;
  final double totalIncome;
  final double totalExpenses;
  final double currentBalance;
  final int totalInvoices;
  final int pendingInvoices;
  final DateTime lastActivity;

  UserStats({
    required this.totalTransactions,
    required this.totalIncome,
    required this.totalExpenses,
    required this.currentBalance,
    required this.totalInvoices,
    required this.pendingInvoices,
    required this.lastActivity,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalTransactions: json['total_transactions'] ?? 0,
      totalIncome: (json['total_income'] ?? 0).toDouble(),
      totalExpenses: (json['total_expenses'] ?? 0).toDouble(),
      currentBalance: (json['current_balance'] ?? 0).toDouble(),
      totalInvoices: json['total_invoices'] ?? 0,
      pendingInvoices: json['pending_invoices'] ?? 0,
      lastActivity: json['last_activity'] != null 
          ? DateTime.parse(json['last_activity']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_transactions': totalTransactions,
      'total_income': totalIncome,
      'total_expenses': totalExpenses,
      'current_balance': currentBalance,
      'total_invoices': totalInvoices,
      'pending_invoices': pendingInvoices,
      'last_activity': lastActivity.toIso8601String(),
    };
  }

  // Calculate savings rate
  double get savingsRate {
    if (totalIncome == 0) return 0;
    return ((totalIncome - totalExpenses) / totalIncome) * 100;
  }

  // Check if user is profitable
  bool get isProfitable => totalIncome > totalExpenses;

  // Get net worth
  double get netWorth => totalIncome - totalExpenses;
}

// User Preferences Model
class UserPreferences {
  final String language;
  final String currency;
  final bool darkMode;
  final bool notifications;
  final bool biometricAuth;
  final String dateFormat;
  final String timeFormat;

  UserPreferences({
    this.language = 'ar',
    this.currency = 'SAR',
    this.darkMode = false,
    this.notifications = true,
    this.biometricAuth = false,
    this.dateFormat = 'dd/MM/yyyy',
    this.timeFormat = '24h',
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      language: json['language'] ?? 'ar',
      currency: json['currency'] ?? 'SAR',
      darkMode: json['dark_mode'] ?? false,
      notifications: json['notifications'] ?? true,
      biometricAuth: json['biometric_auth'] ?? false,
      dateFormat: json['date_format'] ?? 'dd/MM/yyyy',
      timeFormat: json['time_format'] ?? '24h',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'currency': currency,
      'dark_mode': darkMode,
      'notifications': notifications,
      'biometric_auth': biometricAuth,
      'date_format': dateFormat,
      'time_format': timeFormat,
    };
  }

  UserPreferences copyWith({
    String? language,
    String? currency,
    bool? darkMode,
    bool? notifications,
    bool? biometricAuth,
    String? dateFormat,
    String? timeFormat,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      currency: currency ?? this.currency,
      darkMode: darkMode ?? this.darkMode,
      notifications: notifications ?? this.notifications,
      biometricAuth: biometricAuth ?? this.biometricAuth,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
    );
  }
}
