<div appFlowbiteInit>
  <!-- Overview -->
  <div *ngIf="isLoading; else userOverview">
    <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 animate-pulse">
      <div class="container mx-auto">
        <div class="bg-white shadow rounded-lg p-6 flex flex-col md:flex-row gap-4">
          <!-- First Column -->
          <div class="md:w-1/3 space-y-4">
            <!-- User Picture and Info -->
            <div class="text-center">
              <div class="w-32 h-32 rounded-full mx-auto bg-gray-300"></div>
              <div class="mt-2 w-24 h-6 bg-gray-300 mx-auto"></div>
              <div class="w-32 h-4 bg-gray-300 mx-auto mt-1"></div>
              <div class="w-32 h-4 bg-gray-300 mx-auto mt-1"></div>
              <div class="w-32 h-4 bg-gray-300 mx-auto mt-1"></div>
            </div>

            <!-- Icon Buttons -->
            <div class="flex justify-around">
              <div class="w-12 h-12 bg-gray-300 rounded-full"></div>
              <div class="w-12 h-12 bg-gray-300 rounded-full"></div>
              <div class="w-12 h-12 bg-gray-300 rounded-full"></div>
              <div class="w-12 h-12 bg-gray-300 rounded-full"></div>
            </div>

            <!-- Map Container -->
            <div class="border border-gray-300 rounded-lg h-60 bg-gray-300"></div>

            <!-- Lat and LNG Fields -->
            <div class="h-8 bg-gray-300 rounded-lg"></div>
            <div class="mt-2 h-8 bg-gray-300 rounded-lg"></div>
          </div>

          <!-- Second Column -->
          <div class="md:w-2/3 space-y-4">
            <div class="space-y-2">
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
              <div class="w-full h-8 bg-gray-300 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template appFlowbiteInit #userOverview>
    <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
      <div class="container mx-auto">
        <div class="bg-white shadow rounded-lg p-6 flex flex-col md:flex-row gap-4">

          <!-- First Column -->
          <div class="md:w-1/3 space-y-4">
            <!-- User Picture and Info -->
            <div class="text-center">
              <img
                src="https://ui-avatars.com/api/?name={{ userDetails?.data?.firstname }}&background=random&bold=true&color=000000"
                alt="User Picture" class="w-32 h-32 rounded-full mx-auto">
              <h2 class="text-xl font-semibold mt-2">{{ userDetails?.data?.username }}</h2>
              <p class="text-gray-600">{{'user.overview.name' | transloco}}: {{ userDetails?.data?.firstname }}</p>
              <p class="text-gray-600">{{'user.overview.phone' | transloco}}: {{ userDetails?.data?.phone }}</p>
              <p class="text-gray-600">{{'user.overview.phone' | transloco}}: {{ userDetails?.data?.email }}</p>
            </div>

            <!-- Icon Buttons -->
            <div class="flex justify-around space-x-4">
              <button title="{{'user.overview.activate' | transloco}}" data-tooltip-target="tooltip-active"
                routerLink="/users/activate/{{ userDetails?.data?.id }}"
                class="bg-green-500 text-white w-12 h-12 flex items-center justify-center rounded-full">
                <i class="pi pi-power-off" style="font-size: 1.5rem"></i>
              </button>
              <div id="tooltip-active" role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                {{'user.overview.activate' | transloco}}
                <div class="tooltip-arrow" data-popper-arrow></div>
              </div>

              <button title="{{'user.overview.issueInvoice' | transloco}}" data-tooltip-target="tooltip-invoice"
                class="bg-blue-500 text-white w-12 h-12 flex items-center justify-center rounded-full">
                <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                  height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10 3v4a1 1 0 0 1-1 1H5m8-2h3m-3 3h3m-4 3v6m4-3H8M19 4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1ZM8 12v6h8v-6H8Z" />
                </svg>
              </button>
              <div id="tooltip-invoice" role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                {{'user.overview.issueInvoice' | transloco}}
                <div class="tooltip-arrow" data-popper-arrow></div>
              </div>

              <button title="{{'user.overview.disconnect' | transloco}}" data-tooltip-target="tooltip-disconnect"
                class="bg-red-500 text-white w-12 h-12 flex items-center justify-center rounded-full">
                <i class="pi pi-link" style="font-size: 1.5rem"></i>
              </button>
              <div id="tooltip-disconnect" role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                {{'user.overview.disconnect' | transloco}}
                <div class="tooltip-arrow" data-popper-arrow></div>
              </div>

              <a title="{{'user.overview.showQr' | transloco}}" data-tooltip-target="tooltip-qr" (click)="redirectQR()"
                class="bg-violet-500 text-white w-12 h-12 flex items-center justify-center rounded-full cursor-pointer">
                <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                  height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linejoin="round" stroke-width="2"
                    d="M4 4h6v6H4V4Zm10 10h6v6h-6v-6Zm0-10h6v6h-6V4Zm-4 10h.01v.01H10V14Zm0 4h.01v.01H10V18Zm-3 2h.01v.01H7V20Zm0-4h.01v.01H7V16Zm-3 2h.01v.01H4V18Zm0-4h.01v.01H4V14Z" />
                  <path stroke="currentColor" stroke-linejoin="round" stroke-width="2"
                    d="M7 7h.01v.01H7V7Zm10 10h.01v.01H17V17Z" />
                </svg>
              </a>
              <div id="tooltip-qr" role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                {{'user.overview.showQr' | transloco}} <div class="tooltip-arrow" data-popper-arrow></div>
              </div>
            </div>



            <!-- Map Container -->
            <div class="border border-gray-300 rounded-lg h-60 flex items-center justify-center">
              <iframe title="position" class="w-full h-full"
                src="https://www.openstreetmap.org/export/embed.html?bbox=-6.1962890625%2C13.581920900545857%2C66.84082031250001%2C46.22545288226939&amp;layer=mapnik&amp;marker=31.297327991404266%2C30.322265625"></iframe><br /><small>
                <a title="map"
                  href="https://www.openstreetmap.org/?mlat={{ userDetails?.data?.gps_lat }}&amp;mlon={{ userDetails?.data?.gps_lng }}#map=10/{{ userDetails?.data?.gps_lat }}/{{ userDetails?.data?.gps_lng }}"></a></small>
            </div>

            <!-- Lat and LNG Fields -->
            <div>
              <label for="gps_lat" class="block text-gray-600">{{'user.overview.latitude' | transloco}}</label>
              <input id="gps_lat" type="text" value="{{ userDetails?.data?.gps_lat }}"
                class="w-full border border-gray-300 p-2 rounded-lg">
            </div>
            <div class="mt-2">
              <label for="gps_lng" class="block text-gray-600">{{'user.overview.longitude' | transloco}}</label>
              <input id="gps_lng" type="text" value="{{ userDetails?.data?.gps_lng }}"
                class="w-full border border-gray-300 p-2 rounded-lg">
            </div>
          </div>
          <!-- Second Column -->
          <div class="md:w-2/3 space-y-4">
            <ul class="list-group border-b">
              <li class="list-group-item flex justify-between border p-2 ">
                <span class="label font-semibold text-gray-600">{{'user.overview.username' | transloco}}</span>
                <span class="value text-gray-900">{{ userDetails?.data?.username }}</span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.password' | transloco}}</span>
                <span class="value text-gray-900">{{ userDetails?.overview?.password}}</span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.owner' | transloco}}</span>
                <span class="value text-gray-900">{{ userDetails?.data?.parent_username }}</span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.profile' | transloco}}</span>
                <span class="value text-blue-600">{{ userDetails?.overview?.profile_name}}</span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.status' | transloco}}</span>
                <span class="value text-gray-900"><span
                    class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span></span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.remainingTraffic' | transloco}}</span>
                <span class="value text-gray-900"> {{ userDetails?.overview?.remaining_tx ?? 0 | bytesToSize}} </span>
              </li>
              <li class="list-group-item flex justify-between border p-2  py-2">
                <span class="label font-semibold text-gray-600">{{'user.overview.remainingUptime' | transloco}}</span>
                <span class="value text-gray-900">{{ userDetails?.overview?.remaining_uptime ?? '' | timeAgo}}</span>
              </li>
            </ul>
          </div>

        </div>

      </div>
    </div>
  </ng-template>
</div>