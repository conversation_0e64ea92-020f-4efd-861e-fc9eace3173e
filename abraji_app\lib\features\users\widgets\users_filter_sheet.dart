import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/user_data_model.dart';

class UsersFilterSheet extends StatefulWidget {
  final UserFilters currentFilters;
  final Function(UserFilters) onApplyFilters;

  const UsersFilterSheet({
    super.key,
    required this.currentFilters,
    required this.onApplyFilters,
  });

  @override
  State<UsersFilterSheet> createState() => _UsersFilterSheetState();
}

class _UsersFilterSheetState extends State<UsersFilterSheet> {
  late UserFilters _filters;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filters = widget.currentFilters;
    _searchController.text = _filters.search ?? '';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.borderLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  'فلترة المستخدمين',
                  style: AppTypography.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('إعادة تعيين'),
                ),
              ],
            ),
          ),

          // Filters content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // البحث
                  _buildSectionTitle('البحث'),
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث بالاسم أو اسم المستخدم...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onChanged: (value) {
                      _filters = _filters.copyWith(
                        search: value.isEmpty ? null : value,
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // الحالة
                  _buildSectionTitle('الحالة'),
                  _buildStatusFilter(),

                  const SizedBox(height: 24),

                  // الخطة
                  _buildSectionTitle('الخطة'),
                  _buildPlanFilter(),

                  const SizedBox(height: 24),

                  // ترتيب النتائج
                  _buildSectionTitle('ترتيب النتائج'),
                  _buildSortOptions(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: AppColors.borderLight)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: const Text('تطبيق الفلاتر'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: AppTypography.titleMedium.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildStatusFilter() {
    final statuses = [
      {'value': null, 'label': 'جميع الحالات'},
      {'value': 'active', 'label': 'نشط'},
      {'value': 'inactive', 'label': 'غير نشط'},
      {'value': 'suspended', 'label': 'معلق'},
      {'value': 'expired', 'label': 'منتهي الصلاحية'},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: statuses.map((status) {
        final isSelected = _filters.status == status['value'];
        return FilterChip(
          label: Text(status['label'] as String),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _filters = _filters.copyWith(
                status: selected ? status['value'] : null,
              );
            });
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
        );
      }).toList(),
    );
  }

  Widget _buildPlanFilter() {
    final plans = [
      {'value': null, 'label': 'جميع الخطط'},
      {'value': 'Basic', 'label': 'أساسية'},
      {'value': 'Premium', 'label': 'مميزة'},
      {'value': 'Pro', 'label': 'احترافية'},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: plans.map((plan) {
        final isSelected = _filters.plan == plan['value'];
        return FilterChip(
          label: Text(plan['label'] as String),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _filters = _filters.copyWith(
                plan: selected ? plan['value'] : null,
              );
            });
          },
          selectedColor: AppColors.secondary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.secondary,
        );
      }).toList(),
    );
  }

  Widget _buildSortOptions() {
    final sortOptions = [
      {'value': 'created_at', 'label': 'تاريخ الإنشاء'},
      {'value': 'username', 'label': 'اسم المستخدم'},
      {'value': 'last_login', 'label': 'آخر تسجيل دخول'},
      {'value': 'balance', 'label': 'الرصيد'},
    ];

    return Column(
      children: [
        // نوع الترتيب
        DropdownButtonFormField<String>(
          value: _filters.sortBy,
          decoration: InputDecoration(
            labelText: 'ترتيب حسب',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          items: sortOptions.map((option) {
            return DropdownMenuItem(
              value: option['value'],
              child: Text(option['label'] as String),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filters = _filters.copyWith(sortBy: value);
              });
            }
          },
        ),

        const SizedBox(height: 12),

        // اتجاه الترتيب
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('تصاعدي'),
                value: 'asc',
                groupValue: _filters.sortOrder,
                onChanged: (value) {
                  setState(() {
                    _filters = _filters.copyWith(sortOrder: value!);
                  });
                },
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('تنازلي'),
                value: 'desc',
                groupValue: _filters.sortOrder,
                onChanged: (value) {
                  setState(() {
                    _filters = _filters.copyWith(sortOrder: value!);
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _resetFilters() {
    setState(() {
      _filters = UserFilters();
      _searchController.clear();
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(_filters);
    Navigator.of(context).pop();
  }
}
