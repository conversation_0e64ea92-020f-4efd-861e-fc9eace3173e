import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';

class SocialLoginButton extends StatefulWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final double? width;
  final double? height;
  final bool isLoading;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.width,
    this.height,
    this.isLoading = false,
  });

  @override
  State<SocialLoginButton> createState() => _SocialLoginButtonState();
}

class _SocialLoginButtonState extends State<SocialLoginButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Container(
              width: widget.width,
              height: widget.height ?? 48,
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.borderLight, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.iconColor ?? AppColors.textSecondary,
                        ),
                      ),
                    )
                  else
                    Icon(
                      widget.icon,
                      size: 20,
                      color: widget.iconColor ?? AppColors.textSecondary,
                    ),
                  const SizedBox(width: 8),
                  Text(
                    widget.label,
                    style: AppTypography.labelLarge.copyWith(
                      color: widget.textColor ?? AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Predefined Social Login Buttons
class GoogleLoginButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const GoogleLoginButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      icon: Icons.g_mobiledata,
      label: 'Google',
      onPressed: onPressed,
      isLoading: isLoading,
      width: width,
      height: height,
      backgroundColor: Colors.white,
      textColor: AppColors.textPrimary,
      iconColor: const Color(0xFF4285F4),
    );
  }
}

class FacebookLoginButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const FacebookLoginButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      icon: Icons.facebook,
      label: 'Facebook',
      onPressed: onPressed,
      isLoading: isLoading,
      width: width,
      height: height,
      backgroundColor: const Color(0xFF1877F2),
      textColor: Colors.white,
      iconColor: Colors.white,
    );
  }
}

class AppleLoginButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const AppleLoginButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      icon: Icons.apple,
      label: 'Apple',
      onPressed: onPressed,
      isLoading: isLoading,
      width: width,
      height: height,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      iconColor: Colors.white,
    );
  }
}

class TwitterLoginButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const TwitterLoginButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      icon: Icons.alternate_email,
      label: 'Twitter',
      onPressed: onPressed,
      isLoading: isLoading,
      width: width,
      height: height,
      backgroundColor: const Color(0xFF1DA1F2),
      textColor: Colors.white,
      iconColor: Colors.white,
    );
  }
}

// Social Login Row Widget
class SocialLoginRow extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onTwitterPressed;
  final bool showGoogle;
  final bool showFacebook;
  final bool showApple;
  final bool showTwitter;
  final bool isLoading;

  const SocialLoginRow({
    super.key,
    this.onGooglePressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.onTwitterPressed,
    this.showGoogle = true,
    this.showFacebook = true,
    this.showApple = false,
    this.showTwitter = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    if (showGoogle && onGooglePressed != null) {
      buttons.add(
        Expanded(
          child: GoogleLoginButton(
            onPressed: onGooglePressed!,
            isLoading: isLoading,
          ),
        ),
      );
    }

    if (showFacebook && onFacebookPressed != null) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: FacebookLoginButton(
            onPressed: onFacebookPressed!,
            isLoading: isLoading,
          ),
        ),
      );
    }

    if (showApple && onApplePressed != null) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: AppleLoginButton(
            onPressed: onApplePressed!,
            isLoading: isLoading,
          ),
        ),
      );
    }

    if (showTwitter && onTwitterPressed != null) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: TwitterLoginButton(
            onPressed: onTwitterPressed!,
            isLoading: isLoading,
          ),
        ),
      );
    }

    return Row(children: buttons);
  }
}
