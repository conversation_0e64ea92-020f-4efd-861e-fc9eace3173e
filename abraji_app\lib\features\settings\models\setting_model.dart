class Setting {
  final int? id;
  final String key;
  final String value;
  final String? description;
  final String type;
  final bool isPublic;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Setting({
    this.id,
    required this.key,
    required this.value,
    this.description,
    this.type = 'string',
    this.isPublic = false,
    this.createdAt,
    this.updatedAt,
  });

  factory Setting.fromJson(Map<String, dynamic> json) {
    return Setting(
      id: json['id'],
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      description: json['description'],
      type: json['type'] ?? 'string',
      isPublic: json['is_public'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'description': description,
      'type': type,
      'is_public': isPublic,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Setting copyWith({
    int? id,
    String? key,
    String? value,
    String? description,
    String? type,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Setting(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      type: type ?? this.type,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Setting(id: $id, key: $key, value: $value, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Setting && other.id == id && other.key == key;
  }

  @override
  int get hashCode => id.hashCode ^ key.hashCode;
}

// نموذج لاستجابة API
class SettingsResponse {
  final bool success;
  final String message;
  final List<Setting> data;
  final Map<String, dynamic>? meta;

  SettingsResponse({
    required this.success,
    required this.message,
    required this.data,
    this.meta,
  });

  factory SettingsResponse.fromJson(Map<String, dynamic> json) {
    return SettingsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => Setting.fromJson(item))
          .toList() ?? [],
      meta: json['meta'],
    );
  }
}

// نموذج لطلب تحديث الإعدادات
class UpdateSettingRequest {
  final String key;
  final String value;
  final String? description;
  final String? type;
  final bool? isPublic;

  UpdateSettingRequest({
    required this.key,
    required this.value,
    this.description,
    this.type,
    this.isPublic,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'key': key,
      'value': value,
    };
    
    if (description != null) data['description'] = description;
    if (type != null) data['type'] = type;
    if (isPublic != null) data['is_public'] = isPublic;
    
    return data;
  }
}

// ثوابت مفاتيح الإعدادات
class SettingKeys {
  static const String sasRadiusUrl = 'sas_radius_url';
  static const String sasRadiusIp = 'sas_radius_ip';
  static const String sasRadiusPort = 'sas_radius_port';
  static const String sasRadiusSecret = 'sas_radius_secret';
  static const String appName = 'app_name';
  static const String appVersion = 'app_version';
  static const String maintenanceMode = 'maintenance_mode';
  static const String defaultLanguage = 'default_language';
  static const String defaultCurrency = 'default_currency';
  static const String emailNotifications = 'email_notifications';
  static const String smsNotifications = 'sms_notifications';
  static const String pushNotifications = 'push_notifications';
}

// أنواع الإعدادات
enum SettingType {
  string,
  integer,
  boolean,
  json,
  url,
  ip,
  email,
  password,
}

extension SettingTypeExtension on SettingType {
  String get value {
    switch (this) {
      case SettingType.string:
        return 'string';
      case SettingType.integer:
        return 'integer';
      case SettingType.boolean:
        return 'boolean';
      case SettingType.json:
        return 'json';
      case SettingType.url:
        return 'url';
      case SettingType.ip:
        return 'ip';
      case SettingType.email:
        return 'email';
      case SettingType.password:
        return 'password';
    }
  }

  static SettingType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'integer':
        return SettingType.integer;
      case 'boolean':
        return SettingType.boolean;
      case 'json':
        return SettingType.json;
      case 'url':
        return SettingType.url;
      case 'ip':
        return SettingType.ip;
      case 'email':
        return SettingType.email;
      case 'password':
        return SettingType.password;
      default:
        return SettingType.string;
    }
  }
}
