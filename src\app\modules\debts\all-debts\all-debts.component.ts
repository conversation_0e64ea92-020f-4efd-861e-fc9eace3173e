import { Component } from '@angular/core';
import { ColumnState, TableResponse } from '../../../core/common-services/interfaces/table-response';
import { Debt, DebtForm, initialDeptColumnsState } from '../../../core/debts-services/api/debts';
import { DebtsService } from '../../../core/debts-services/services/debts.service';
import { ToastService } from '../../shared/toast/toast.service';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-all-debts',
  templateUrl: './all-debts.component.html',
  styleUrl: './all-debts.component.scss'
})
export class AllDebtsComponent {
  selectedDebt!: Debt;
  editForm!: FormGroup;
  payForm!: FormGroup;
  requestForm!: DebtForm;
  isLoading: boolean = false;
  debtColumnsState!: ColumnState[];
  tableResponse: TableResponse<Debt> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };

  constructor(
    protected debtsService: DebtsService,
    protected toastService: ToastService,
    protected tableElementsService: TableElementsService,
    protected localStorageService: LocalStorageService,
    protected fb: FormBuilder
  ) {}

  // Bind search value to form
  searchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.requestForm.search = inputElement.value;
    this.requestForm.page = 1;
    this.loadDebts();
  }

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.requestForm);
    // fetch data
    this.loadDebts();
  }

  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(this.tableResponse.last_page, this.requestForm.page);
  }


  // Bind the change page in pagination to the form
  changePage(page: (string | number)): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.requestForm.page = pageNumber;
      this.loadDebts();
    }
  }

  // Bind the change in columns visibility
  toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(columnKey, this.debtColumnsState, this.localStorageService.DeptColumnsState);
  }

  ngOnInit(): void {
    // initiate columns state
    this.debtColumnsState = this.localStorageService
      .loadColumnsState(this.localStorageService.DeptColumnsState)
      || initialDeptColumnsState;

    this.requestForm = {
      page: 1,
      count: 10,
      sortBy: "",
      direction: "",
      search: "",
      columns: [],
      pay: null,
    };
    console.log(this.requestForm);

    // fetch users
    this.loadDebts();
    this.initEditForm();
    this.initPayForm();
  }

  getVisibleColumns(): string[] {
    return this.tableElementsService.getVisibleColumns(this.debtColumnsState);
  }

  getPropertyValue(debt: Debt, key: string): any {
    switch (key) {
      default:
        return debt[key as keyof Debt];
    }
  }

  loadDebts(): void {
    console.log("Loading Debts", this.requestForm);

    this.isLoading = true;
    this.debtsService.getDebts(this.requestForm).subscribe({
      next: (response: any) => {
        console.log(response);
        this.tableResponse = response;
        this.isLoading = false;
    },
    error: (error: any) => {
      this.isLoading = false;
      this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the data');
      console.error('There was an error!', error);
    },
    });
  }

  selectDebt(debt: Debt): void {
    this.selectedDebt = debt;
    // set editForm values
    this.amount.setValue(debt.amount);
    this.amount.setValidators([Validators.required, Validators.min(Math.max(0, Number(debt.amount_paid)))]);
    this.description.setValue(debt.description);
    this.debt_timestamp.setValue(debt.debt_timestamp);
    // set payForm values
    this.amountToPay.setValidators([Validators.required, Validators.min(0.01), Validators.max(Math.max(0, Number(debt.amount) - Number(debt.amount_paid)))]);
    this.resetPayForm();
  }

  onPayChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.requestForm.pay = selectElement.value === "-1" ? null : selectElement.value === "1" ? true : false;
  }

  togglePayAll(): void {
    if (this.payForm.get('payAll')?.value) {
      this.amountToPay?.disable();
      this.amountToPay?.setValue(Math.max(0, Number(this.selectedDebt.amount) - Number(this.selectedDebt.amount_paid)));
    } else {
      this.amountToPay?.enable();
    }
  }
  resetPayForm(): void {
    this.payForm.reset();
    this.payForm.get('payAll')?.setValue(false);
    this.amountToPay?.enable();
  }

  // init editForm
  initEditForm():void {
    // init editForm with attributes amount, description and debt_timestamp
    this.editForm  = this.fb.group ({
      // validate amount as required and its value is bigger than 0
      amount: this.fb.control('', [Validators.required, Validators.min(0)]),
      description: this.fb.control('', []),
      debt_timestamp: this.fb.control('', [Validators.required]),
    });
  }

  // init payForm
  initPayForm(): void {
    // init payForm with attribute amount
    this.payForm = this.fb.group({
      amount: this.fb.control('', [Validators.required]),
      payAll: this.fb.control(false),
    });
  }

  // pay debt
  payDept(): void {
    this.debtsService.payDebt(Number(this.selectedDebt?.id)).subscribe({
      next: (response: any) => {
        console.log(response);
        this.toastService.addToast('success', 'Success Message', 'Debt paid successfully');
        this.loadDebts();
      },
      error: (error: any) => {
        this.toastService.addToast('error', 'Error Message', 'There was an error on paying the debt');
        console.error('There was an error!', error);
      },
    });
  }

  payPartialDept(): void {
    if (!this.payForm.invalid) {
      this.debtsService.payPartialDebt(this.selectedDebt?.id, this.amountToPay?.value).subscribe({
        next: (response: any) => {
          console.log(response);
          this.toastService.addToast('success', 'Success Message', 'Debt paid successfully');
          this.loadDebts();
        },
        error: (error: any) => {
          this.toastService.addToast('error', 'Error Message', error.error.message);
          console.error('There was an error!', error);
        },
      });
    }
    else {
      console.log(this.payForm);
      this.toastService.addToast('error', 'Error Message', 'Please fill all required fields');
    }
  }

  editDept(): void {
    if (!this.editForm.invalid) {
      const obj = {id: this.selectedDebt?.id, amount: this.amount.value, description: this.description.value, debt_timestamp: this.debt_timestamp.value};
      this.debtsService.updateDebt(obj).subscribe({
        next: (response: any) => {
          console.log(response);
          // update the selected debt with the new values
          this.updateSelectedDebtValues(response);

          this.toastService.addToast('success', 'Success Message', 'Debt updated successfully');
          //this.loadDebts();
        },
        error: (error: any) => {
          this.toastService.addToast('error', 'Error Message', error.error.message);
          console.error('There was an error!', error);
        },
      });
    }
    else {
      console.log(this.editForm);
      this.toastService.addToast('error', 'Error Message', 'Please fill all required fields');
    }
  }



  private updateSelectedDebtValues(response: any) {
    this.selectedDebt.amount = response.debt.amount;
    this.selectedDebt.description = response.debt.description;
    this.selectedDebt.debt_timestamp = response.debt.debt_timestamp;
    this.selectedDebt.pay = response.debt.pay;
    this.selectedDebt.paid_at = response.debt.paid_at;
  }

  // Getters
  get amount(): any {
    return this.editForm.get('amount');
  }
  get description(): any {
    return this.editForm.get('description');
  }
  get debt_timestamp(): any {
    return this.editForm.get('debt_timestamp');
  }

  get amountToPay(): any {
    return this.payForm.get('amount');
  }
  get payAll(): any {
    return this.payForm.get('payAll');
  }
  getCalculatedMaxPaidValue(): number {
    return Number(this.selectedDebt.amount) - Number(this.selectedDebt.amount_paid);
  }
}
