import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, shareReplay } from 'rxjs/operators';
import { HttpService } from '../../common-services/services/http.service';
import { Statistics, CardsResponse, TransactionsResponse } from './stats';
import { CacheService } from '../../common-services/services/cache.service';

@Injectable({
  providedIn: 'root',
})
export class StatsApi {
  private apiUrl = `dashboard`;
  private httpService = inject(HttpService);
  private cacheService = inject(CacheService);

  private statsCache$: Observable<Statistics> | null = null;
  private cardsCache$: Observable<CardsResponse> | null = null;
  private transactionsCache$: Observable<TransactionsResponse> | null = null;

  constructor() {
    this.cacheService.registerCacheClearCallback(() => this.clearCache());
  }

  getStats(): Observable<Statistics> {
    if (!this.statsCache$) {
      this.statsCache$ = this.httpService.get(`${this.apiUrl}`).pipe(
        shareReplay(1),
        catchError((error) => {
          this.statsCache$ = null;
          return of(error);
        })
      );
    }
    return this.statsCache$;
  }

  getCards(): Observable<CardsResponse> {
    if (!this.cardsCache$) {
      this.cardsCache$ = this.httpService.get(`${this.apiUrl}/cards`).pipe(
        shareReplay(1),
        catchError((error) => {
          this.cardsCache$ = null;
          return of(error);
        })
      );
    }
    return this.cardsCache$;
  }

  getTransactions(): Observable<TransactionsResponse> {
    if (!this.transactionsCache$) {
      this.transactionsCache$ = this.httpService
        .get(`${this.apiUrl}/transactions`)
        .pipe(
          shareReplay(1),
          catchError((error) => {
            this.transactionsCache$ = null;
            return of(error);
          })
        );
    }
    return this.transactionsCache$;
  }

  clearCache(): void {
    this.statsCache$ = null;
    this.cardsCache$ = null;
    this.transactionsCache$ = null;
  }
}
