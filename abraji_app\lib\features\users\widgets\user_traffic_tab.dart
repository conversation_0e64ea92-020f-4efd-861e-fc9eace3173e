import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';

class UserTrafficTab extends StatefulWidget {
  final String userId;

  const UserTrafficTab({
    super.key,
    required this.userId,
  });

  @override
  State<UserTrafficTab> createState() => _UserTrafficTabState();
}

class _UserTrafficTabState extends State<UserTrafficTab> {
  List<TrafficData> trafficData = [];
  bool isLoading = true;
  String selectedPeriod = 'today';

  @override
  void initState() {
    super.initState();
    _loadTrafficData();
  }

  Future<void> _loadTrafficData() async {
    setState(() {
      isLoading = true;
    });

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      trafficData = _generateMockTrafficData();
      isLoading = false;
    });
  }

  List<TrafficData> _generateMockTrafficData() {
    final now = DateTime.now();
    return [
      TrafficData(
        timestamp: now.subtract(const Duration(hours: 1)),
        downloadBytes: 1024 * 1024 * 150, // 150 MB
        uploadBytes: 1024 * 1024 * 25,   // 25 MB
        sessionId: 'session_1',
      ),
      TrafficData(
        timestamp: now.subtract(const Duration(hours: 2)),
        downloadBytes: 1024 * 1024 * 200, // 200 MB
        uploadBytes: 1024 * 1024 * 30,   // 30 MB
        sessionId: 'session_2',
      ),
      TrafficData(
        timestamp: now.subtract(const Duration(hours: 3)),
        downloadBytes: 1024 * 1024 * 100, // 100 MB
        uploadBytes: 1024 * 1024 * 15,   // 15 MB
        sessionId: 'session_3',
      ),
      TrafficData(
        timestamp: now.subtract(const Duration(hours: 4)),
        downloadBytes: 1024 * 1024 * 300, // 300 MB
        uploadBytes: 1024 * 1024 * 45,   // 45 MB
        sessionId: 'session_4',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadTrafficData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // فلتر الفترة الزمنية
            _buildPeriodFilter(),
            
            const SizedBox(height: 20),
            
            // إحصائيات حركة البيانات
            _buildTrafficStats(),
            
            const SizedBox(height: 20),
            
            // رسم بياني لحركة البيانات
            _buildTrafficChart(),
            
            const SizedBox(height: 20),
            
            // تفاصيل حركة البيانات
            Text(
              'تفاصيل حركة البيانات',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (trafficData.isEmpty)
              _buildEmptyState()
            else
              ...trafficData.map((data) => _buildTrafficCard(data)),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodFilter() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.date_range, color: AppColors.primary),
            const SizedBox(width: 12),
            Text(
              'الفترة الزمنية:',
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButton<String>(
                value: selectedPeriod,
                isExpanded: true,
                underline: const SizedBox(),
                items: const [
                  DropdownMenuItem(value: 'today', child: Text('اليوم')),
                  DropdownMenuItem(value: 'week', child: Text('هذا الأسبوع')),
                  DropdownMenuItem(value: 'month', child: Text('هذا الشهر')),
                  DropdownMenuItem(value: 'year', child: Text('هذا العام')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedPeriod = value;
                    });
                    _loadTrafficData();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficStats() {
    final totalDownload = trafficData.fold<int>(0, (sum, data) => sum + data.downloadBytes);
    final totalUpload = trafficData.fold<int>(0, (sum, data) => sum + data.uploadBytes);
    final totalTraffic = totalDownload + totalUpload;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.data_usage,
                    color: AppColors.secondary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات حركة البيانات',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي البيانات',
                    _formatBytes(totalTraffic),
                    Icons.data_usage,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'التحميل',
                    _formatBytes(totalDownload),
                    Icons.download,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الرفع',
                    _formatBytes(totalUpload),
                    Icons.upload,
                    AppColors.warning,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'الجلسات',
                    trafficData.length.toString(),
                    Icons.wifi,
                    AppColors.accent,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrafficChart() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.show_chart,
                    color: AppColors.accent,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'الرسم البياني لحركة البيانات',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 48,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'الرسم البياني قيد التطوير',
                      style: AppTypography.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficCard(TrafficData data) {
    final totalBytes = data.downloadBytes + data.uploadBytes;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  _formatDateTime(data.timestamp),
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    data.sessionId,
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildTrafficInfo(
                    'إجمالي البيانات',
                    _formatBytes(totalBytes),
                    Icons.data_usage,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildTrafficInfo(
                    'التحميل',
                    _formatBytes(data.downloadBytes),
                    Icons.download,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildTrafficInfo(
                    'الرفع',
                    _formatBytes(data.uploadBytes),
                    Icons.upload,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficInfo(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: AppTypography.bodySmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            Icons.data_usage_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات حركة',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد بيانات حركة للفترة المحددة',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

// نموذج بيانات حركة البيانات
class TrafficData {
  final DateTime timestamp;
  final int downloadBytes;
  final int uploadBytes;
  final String sessionId;

  TrafficData({
    required this.timestamp,
    required this.downloadBytes,
    required this.uploadBytes,
    required this.sessionId,
  });
}
