import { inject } from '@angular/core';
import { CanActivateChildFn, Router } from '@angular/router';
import { AuthService } from '../auth-services/services/auth.service';

export const authGuard: CanActivateChildFn = (childRoute, state) => {
  var authService = inject(AuthService);
  const router = inject(Router);

  if (authService.isAuthenticated()){
    return true;
  }
  authService.redirectTo = state.url;
  router.navigate(['/login']);
  return false;
};
