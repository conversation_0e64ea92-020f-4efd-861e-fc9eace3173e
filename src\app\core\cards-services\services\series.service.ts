// src/app/core/cards-services-api/series.service.ts
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeriesApi } from '../api/series.api';
import { EncryptionService } from '../../common-services/services/encryption.service';
import { TableResponse } from '../../common-services/interfaces/table-response';
import { CardDetails, SeriesDetails, SeriesData, SeriesForm } from '../api/series';

@Injectable({
  providedIn: 'root',
})
export class SeriesService implements SeriesData {
  private seriesApi = inject(SeriesApi);
  private encryptionService = inject(EncryptionService);

  getSeries(requestForm: SeriesForm): Observable<TableResponse<SeriesDetails>> {
    const payload = this.encryptionService.generatePayload(requestForm);
    return this.seriesApi.getSeries(payload);
  }

  getCardById(id: string): Observable<SeriesDetails> {
    return this.seriesApi.getCardById(id);
  }
  getListCardForSeries(id: string, requestForm: any): Observable<TableResponse<CardDetails>> {
    return this.seriesApi.getListCardForSeries(id, requestForm);
  }

  mapCardDetailsToExportFormat(data: CardDetails[]): any[] {
    return data.map(card => ({
      id: card.id,
      serialnumber: card.serialnumber,
      pin: card.pin,
      username: card.username,
      password: card.password,
      used_at: card.used_at,
      used_by_user: card.user_details.username,
      used_by_manager: card.manager_details.username,
    }));
  }
}
