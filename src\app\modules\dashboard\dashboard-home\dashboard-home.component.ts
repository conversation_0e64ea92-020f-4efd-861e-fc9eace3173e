import { Component, inject, OnInit } from '@angular/core';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';

@Component({
  selector: 'app-dashboard-home',
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.scss']
})
export class DashboardHomeComponent implements OnInit {

  // Test to see if localStorage is contains values expected
  private localStorageService = inject(LocalStorageService);

  ngOnInit(): void {
    console.log(localStorage);
  }
}
