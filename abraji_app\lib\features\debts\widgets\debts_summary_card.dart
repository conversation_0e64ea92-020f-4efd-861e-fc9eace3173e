import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/debt_models.dart';

class DebtsSummaryCard extends StatelessWidget {
  final DebtsSummary summary;

  const DebtsSummaryCard({
    super.key,
    required this.summary,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Text(
              'ملخص الديون',
              style: AppTypography.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // إجمالي المبلغ
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الديون',
                      style: AppTypography.bodyMedium.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    Text(
                      '${summary.totalAmount.toStringAsFixed(2)} ر.س',
                      style: AppTypography.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مدفوع',
                    summary.paidAmount,
                    summary.paidCount,
                    Icons.check_circle,
                    Colors.green.shade300,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    'معلق',
                    summary.pendingAmount,
                    summary.pendingCount,
                    Icons.pending,
                    Colors.orange.shade300,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'متأخر',
                    summary.overdueAmount,
                    summary.overdueCount,
                    Icons.error,
                    Colors.red.shade300,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    'المجموع',
                    summary.totalAmount,
                    summary.totalCount,
                    Icons.receipt_long,
                    Colors.blue.shade300,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // شريط التقدم
            _buildProgressBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    double amount,
    int count,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  label,
                  style: AppTypography.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${amount.toStringAsFixed(0)} ر.س',
            style: AppTypography.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            '$count عنصر',
            style: AppTypography.bodySmall.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final paidPercentage = summary.totalAmount > 0 
        ? (summary.paidAmount / summary.totalAmount) * 100
        : 0.0;
    final overduePercentage = summary.totalAmount > 0 
        ? (summary.overdueAmount / summary.totalAmount) * 100
        : 0.0;
    final pendingPercentage = summary.totalAmount > 0 
        ? (summary.pendingAmount / summary.totalAmount) * 100
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'توزيع الديون',
          style: AppTypography.bodyMedium.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.white.withValues(alpha: 0.2),
          ),
          child: Row(
            children: [
              if (paidPercentage > 0)
                Expanded(
                  flex: paidPercentage.round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.green.shade300,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        bottomLeft: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
              if (pendingPercentage > 0)
                Expanded(
                  flex: pendingPercentage.round(),
                  child: Container(
                    color: Colors.orange.shade300,
                  ),
                ),
              if (overduePercentage > 0)
                Expanded(
                  flex: overduePercentage.round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red.shade300,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(4),
                        bottomRight: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildLegendItem('مدفوع', Colors.green.shade300, '${paidPercentage.toStringAsFixed(1)}%'),
            const SizedBox(width: 16),
            _buildLegendItem('معلق', Colors.orange.shade300, '${pendingPercentage.toStringAsFixed(1)}%'),
            const SizedBox(width: 16),
            _buildLegendItem('متأخر', Colors.red.shade300, '${overduePercentage.toStringAsFixed(1)}%'),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, String percentage) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ($percentage)',
          style: AppTypography.bodySmall.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }
}
