
<div appFlowbiteInit>
  <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
    <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
      <div class="overflow-x-auto whitespace-nowrap scrollbar-hide">
        <ul class="flex flex-nowrap -mb-px bg-white text-sm font-medium text-center">
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="overview" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.overview' | transloco }}</a>
          </li>
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="edit" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.edit' | transloco }}</a>
          </li>
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="traffic" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.traffic' | transloco }}</a>
          </li>
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="sessions" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.sessions' | transloco }}</a>
          </li>
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="debts" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.debts' | transloco }}</a>
          </li>
          <li class="me-2" >
            <a class="inline-block p-4 border-b-2 rounded-t-lg" routerLink="invoices" routerLinkActive="text-purple-600 border-purple-600">{{ 'user.nav.invoices' | transloco }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div>

  <router-outlet></router-outlet>

</div>



<style>
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
  }
</style>
