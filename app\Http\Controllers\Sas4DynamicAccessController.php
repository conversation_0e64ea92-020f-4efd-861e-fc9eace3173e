<?php

namespace App\Http\Controllers;

use App\Services\Sas4DynamicService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Exception;

class Sas4DynamicAccessController extends Controller
{
    protected $sas4Service;

    public function __construct(Sas4DynamicService $sas4Service)
    {
        $this->sas4Service = $sas4Service;
    }

    /**
     * اتصال ديناميكي مع لوحة SAS4
     */
    public function connectToSas4(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sas4_ip' => 'required|ip',
            'username' => 'required|string|min:3|max:50',
            'password' => 'required|string|min:3',
            'endpoint' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $sas4Ip = $request->input('sas4_ip');
            $username = $request->input('username');
            $password = $request->input('password');
            $endpoint = $request->input('endpoint', 'dashboard'); // افتراضي

            // محاولة تسجيل الدخول والحصول على البيانات
            $result = $this->sas4Service->authenticateAndFetchData(
                $sas4Ip,
                $username,
                $password,
                $endpoint
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم الاتصال بنجاح مع لوحة SAS4',
                    'data' => [
                        'sas4_ip' => $sas4Ip,
                        'username' => $username,
                        'endpoint' => $endpoint,
                        'token' => $result['token'],
                        'response_data' => $result['data'],
                        'connection_time' => $result['connection_time'],
                        'response_time' => $result['response_time'],
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال مع لوحة SAS4',
                    'error' => $result['error'],
                    'details' => $result['details'] ?? null
                ], 401);
            }

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في الخادم',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار الاتصال مع لوحة SAS4 (بدون جلب بيانات)
     */
    public function testSas4Connection(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sas4_ip' => 'required|ip',
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->sas4Service->testConnection(
                $request->input('sas4_ip'),
                $request->input('username'),
                $request->input('password')
            );

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في اختبار الاتصال',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات من endpoint محدد (يتطلب token موجود)
     */
    public function fetchSas4Data(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sas4_ip' => 'required|ip',
            'token' => 'required|string',
            'endpoint' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->sas4Service->fetchDataWithToken(
                $request->input('sas4_ip'),
                $request->input('token'),
                $request->input('endpoint')
            );

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب البيانات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على قائمة endpoints المتاحة
     */
    public function getAvailableEndpoints(): JsonResponse
    {
        $endpoints = $this->sas4Service->getAvailableEndpoints();

        return response()->json([
            'success' => true,
            'data' => $endpoints
        ]);
    }

    /**
     * اختبار تشفير payload
     */
    public function testEncryption(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $payload = $this->sas4Service->encryptPayload([
                'username' => $request->input('username'),
                'password' => $request->input('password'),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء payload مشفر',
                'data' => [
                    'encrypted_payload' => $payload,
                    'encryption_method' => 'AES-256-CBC',
                    'timestamp' => now()->toISOString(),
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في التشفير',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
