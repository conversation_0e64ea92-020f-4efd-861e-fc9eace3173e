import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ResourcesRoutingModule } from './resources-routing.module';
import { TranslocoModule } from '@jsverse/transloco';
import { TableSkeletonComponent } from '../shared/skeletons/table/table.component';
import { ExpensesComponent } from './expenses/expenses.component';
import { SharedModule } from '../shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [ExpensesComponent],
  imports: [
    CommonModule,
    ResourcesRoutingModule,
    TranslocoModule,
    TableSkeletonComponent,
    SharedModule,
    ReactiveFormsModule,
  ],
})
export class ResourcesModule {}
