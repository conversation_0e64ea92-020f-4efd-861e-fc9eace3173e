import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DebtsApi } from './api/debts.api';
import { DebtsService } from './services/debts.service';
import { IDebtsService } from './services/IDebtsService';


const API = [DebtsApi];

const SERVICES = [
  {provide: IDebtsService , useClass: DebtsService}
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class DebtsServicesModule {
  static forRoot(): ModuleWithProviders<DebtsServicesModule> {
    return {
      ngModule: DebtsServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
