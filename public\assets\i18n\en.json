{"logo": "<PERSON><PERSON><PERSON>", "validationErrors": {"required": "This field is required", "passwordMismatch": "Password does not match", "min": "Minimum value is {{value}}", "max": "Maximum value is {{value}}"}, "header": {"addNewAccount": "Add New Account", "accountSettings": "Account <PERSON><PERSON>", "switchAccount": "Switch Account", "signOut": "Sign Out"}, "common": {"loading": "Loading ...", "print": "Print", "poweredBy": "Powered By", "iqd": "IQD", "copyright": "All Rights Reserved", "cancel": "Cancel", "confirm": "Confirm", "edit": "Edit", "search": "Search", "delete": "Delete", "save": "Save", "create": "Create", "add": "Add", "viewAll": "View All", "all": "All", "changeLanguage": "Change Language", "settings": "Settings Page", "logout": "Logout"}, "date": {"today": "Today", "yesterday": "Yesterday", "last7days": "Last 7 Days", "last30days": "Last 30 Days", "thisMonth": "This Month", "lastMonth": "Last Month", "months": {"Jan": "January", "Feb": "February", "Mar": "March", "Apr": "April", "May": "May", "Jun": "June", "Jul": "July", "Aug": "August", "Sep": "September", "Oct": "October", "Nov": "November", "Dec": "December"}}, "table": {"foundRecord": "Total Records", "filter": "Filter", "advancedFilter": "Advanced Filter", "columns": "Columns", "showColumns": "Show Columns", "search": "Search", "showing": "Showing", "of": "of", "actions": "Actions", "undefined": "undefined"}, "auth": {"title": "Login to your account", "login": "<PERSON><PERSON>", "loginLoading": "Signing in ...", "username": "User Name", "email": "Email", "password": "Password", "rememberMe": "Remember Me", "name": "Name", "logout": "Logout"}, "dashboard": {"totalUsers": "Total Users", "onlineUsers": "Online Users", "activeUsers": "Active Users", "expiredUsers": "Expired Users", "balance": "Wallet Balance", "aboutExpire": "Expire In 3 Days", "expireToday": "Expiring Today", "onlineFUP": "online FUP", "maintenance": "Maintenance", "cardsPurchase": "Cards Purchase", "generalExpenses": "General Expenses", "total": "Total", "remaining": "Remaining", "used": "Used", "cardProfile": "No. Card"}, "sidebar": {"dashboard": "Dashboard", "users": "Users", "usersList": "Users List", "onlineUsersList": "Online Users List", "activeUsers": "Active Users", "cardsSystem": "Cards System", "billing": "Billing", "invoices": "Invoices", "debts": "Debts", "userInvoices": "User Invoices", "issueInvoices": "Issue Invoices", "resources": "Resources", "maintenance": "Maintenance", "messages": "Messages", "wallet": "Wallet"}, "user": {"nav": {"overview": "Overview", "edit": "Edit", "traffic": "Traffic", "invoices": "Invoices", "debts": "Debts", "sessions": "Sessions"}, "table": {"newAccount": "Create Account", "export": "Export to file", "edit": "Edit", "activate": "Activate", "extendQuota": "Extend quota", "changeQuota": "Change quota", "payDebt": "Pay Debt", "active": "Active", "expired": "Expired", "filter": {"active": "Active", "expired": "Expired", "disabled": "Expired", "expiringToday": "Expired", "expiringSoon": "Expired", "online": "Online", "offline": "Offline", "subUser": "Sub-users", "connection": "connection", "quota": "<PERSON><PERSON><PERSON>", "status": "Status"}}, "overview": {"overview": "overview", "username": "Username", "firstname": "First Name", "lastname": "Last Name", "name": "Name", "email": "Email", "street": "Street", "phone": "Phone", "password": "Password", "balance": "Balance", "owner": "Owner", "profile": "Profile", "expiration": "Expiration", "debtDays": "Debt Days", "incorrectPin": "Incorrect PIN tries", "status": "Status", "lastSeen": "Last Seen Online", "remainingDownload": "Remaining Download", "remainingUpload": "Remaining Upload", "remainingTraffic": "Remaining Traffic", "remainingUptime": "Remaining Uptime", "totalPurchase": "Total Purchases", "createdOn": "Created On", "createdBy": "Created By", "longitude": "Longitude", "latitude": "Latitude", "activate": "Activate", "inactive": "Inactive", "issueInvoice": "Issue Invoices", "disconnect": "Disconnect", "showQr": "Show Qr", "id": "ID", "parent_username": "<PERSON><PERSON>", "loan_balance": "debt", "group": "Group", "mac": "MAC", "daily_traffic": "Daily Traffic", "city": "City", "remaining_days": "Remaining Days", "static_ip": "Static IP", "notes": "Notes", "last_online": "Last Online", "company": "Company", "simultaneous_sessions": "Simultaneous Sessions", "used_traffic": "Used Traffic", "address": "Address", "contract_id": "Contract ID", "created_at": "Created At", "available_traffic": "Available Traffic", "national_id": "National ID", "mikrotik_ipv6_prefix": "IPv6 Prefix", "site": "Site", "apartment": "Apartment", "acctinputoctets": "Upload", "acctoutputoctets": "Download", "nasipaddress": "NAS", "framedipaddress": "IP", "callingstationid": "MAC", "daily_usage_percentage": "Daily Quota", "acctstarttime": "Start Time", "oui": "<PERSON><PERSON>", "calledstationid": "Service"}, "edit": {"edit": "Edit", "basicInformation": "Basic Information", "personalInformation": "Personal Information", "additionalInformation": "Additional Information", "save": "Save", "confirmPassword": "Confirm Password", "portalPassword": "Portal Password", "useSeparatePortalPassword": "Use separate portal password", "enableAccount": "Enable Account"}, "traffic": {"traffic": "Traffic", "daily": "Daily", "monthly": "Monthly", "day": "Day", "rx": "Download", "tx": "Upload", "total": "Total", "total_real": "Total Real", "free_traffic": "Free Traffic", "uptime": "Uptime", "remaining": "Remaining", "totalTraffic": "Total Traffic", "totalDownload": "Total Download", "totalUpload": "Total Upload", "totalUptime": "Total Uptime", "totalRemaining": "Total Remaining"}, "quota": {"changeQuota": "Change Quota", "endsDate": "Ends At", "newQuota": "New Quota", "currentQuota": "Current Quota", "changeTime": "Change Date", "now": "Now", "change": "Change", "username": "Username"}}, "resources": {"expenses": "Expenses", "addExpenses": "Add Expenses", "id": "ID", "descriptionPlaceholder": "Write Expenses description here", "expensesDescription": "Expenses Description", "selectCategory": "Select Category", "price": "Price", "category": "Category", "maintenance": "Maintenance", "cardsPurchase": "Cards Purchase", "generalExpenses": "General Expenses", "iqd": "IQD", "expense": "Expense", "details": "Details", "type": "Type", "added_by": "Added By", "amount": "Amount", "description": "Description", "date": "Date", "value": "Value", "cost": "Cost", "edit": "Edit", "createNewExpenses": "Create New Expenses", "editExpenses": "Edit Expenses", "other": "Other", "created_at": "Created At", "updated_at": "Updated At"}, "invoices": {"invoices": "Invoices", "issueInvoice": "Issue Invoices"}, "debts": {"debt": "Debt", "debts": "Debts", "payDebt": "Pay Debt", "issueDebt": "Issue Debt", "user_id": "User ID", "username": "Username", "amount": "Amount", "totalAmount": "Total Amount", "amount_paid": "Amount <PERSON>", "description": "Description", "date": "Date", "debt_timestamp": "Date", "pay": "Pay", "payAll": "Pay All Remaining Debts", "paid_at": "<PERSON><PERSON>", "history_of_payments": "History of Payments", "paid": "Paid", "unpaid": "Unpaid", "created_at": "Created At", "updated_at": "Updated At", "edit": "Edit", "confirmPayment": "Are you sure you want to confirm payment?", "confirmPaymentSuccess": "Payment confirmed successfully", "confirmPaymentError": "Error confirming payment", "number_of_debts_done": "Number of Debts Done", "number_of_debts_not_done": "Number of Debts Not Done", "amount_of_money_paid": "Amount of Money Paid", "amount_of_money_not_paid": "Amount of Money Not Paid"}, "card": {"series": "Series", "type": "Type", "owner": "Owner", "value": "Value", "expiration": "Expiration", "qty": "Quantity", "used": "Used", "createdBy": "created By", "series_date": "Added Date", "suspended": "Suspended", "profile": "Profile", "refillCard": "Refill Card", "prepaidUser": "Prepaid User", "cardDetails": {"id": "ID", "serialnumber": "Serial Number", "pin": "PIN", "username": "Username", "password": "Password", "used_at": "Used At", "used_by_user": "Used By User", "used_by_manager": "Used By Manager"}}, "wallet": {"currentBalance": "Current Balance", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transaction": "Transaction", "walletTransactions": "Wallet Transactions", "resetBalance": "Reset Balance", "editBalance": "Edit Balance", "transactionType": "Transaction Type", "wallet": "Wallet", "balance": "Balance", "amount": "Amount", "type": "Type", "created_at": "Created At", "updated_at": "Updated At", "debit": "Debit", "transaction_id": "Transaction ID", "debt_id": "Debt ID", "credit": "Credit", "admin_id": "Admin ID", "deleted_at": "Deleted At", "iqd": "IQD", "TransactionEmpty": "No Transaction Found", "noTransactions": "Spend money to See your New Transactions", "resetConfirmation": "Are you sure you want to reset balance?"}}