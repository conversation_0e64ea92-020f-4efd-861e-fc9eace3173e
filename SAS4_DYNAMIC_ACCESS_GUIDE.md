# 🚀 دليل استخدام SAS4 Dynamic Access API

## 📋 نظرة عامة
نظام متكامل للاتصال الديناميكي مع لوحة SAS4 باستخدام تشفير AES-256-CBC والحصول على البيانات من endpoints مختلفة.

## 🔧 الإعداد

### 1. تحديث ملف .env:
```env
# SAS4 Dynamic Access Configuration
SAS4_ENCRYPTION_METHOD=AES-256-CBC
SAS4_ENCRYPTION_KEY=your-actual-32-character-key-here
SAS4_ENCRYPTION_IV=your-actual-16-iv
SAS4_CONNECTION_TIMEOUT=30
SAS4_READ_TIMEOUT=60
SAS4_USER_AGENT="AbrajiAPIs-SAS4-Client/1.0"
```

### 2. مسح Cache:
```bash
php artisan config:clear
php artisan route:clear
```

## 🎯 API Endpoints

### 1. الاتصال الكامل (تسجيل دخول + جلب بيانات):
```
POST /api/sas4/connect
```

**Body:**
```json
{
    "sas4_ip": "*************",
    "username": "admin",
    "password": "admin123",
    "endpoint": "dashboard"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم الاتصال بنجاح مع لوحة SAS4",
    "data": {
        "sas4_ip": "*************",
        "username": "admin",
        "endpoint": "dashboard",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
        "response_data": {...},
        "connection_time": 1250.5,
        "response_time": 890.2
    }
}
```

### 2. اختبار الاتصال:
```
POST /api/sas4/test-connection
```

**Body:**
```json
{
    "sas4_ip": "*************",
    "username": "admin",
    "password": "admin123"
}
```

### 3. جلب البيانات (مع token موجود):
```
POST /api/sas4/fetch-data
```

**Body:**
```json
{
    "sas4_ip": "*************",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "endpoint": "users/table"
}
```

### 4. قائمة Endpoints المتاحة:
```
GET /api/sas4/endpoints
```

### 5. اختبار التشفير:
```
POST /api/sas4/test-encryption
```

**Body:**
```json
{
    "username": "admin",
    "password": "admin123"
}
```

## 🔐 التشفير

### طريقة التشفير:
- **Algorithm**: AES-256-CBC
- **Key**: SHA256 hash من المفتاح المحدد
- **IV**: أول 16 بايت من SHA256 hash للـ IV
- **Output**: Base64 encoded

### مثال على Payload مشفر:
```json
{
    "payload": "eyJpdiI6IlA0QWJKU3hnTXRVVGh6ZXJQT0tSN0E9PSIsInZhbHVlIjoiWXpZVFM4Q0g5UGFQUXFGNUh6RFFmdz09IiwibWFjIjoiNDE1MDViYTczZGQwM2ZhODUyYWFhYWRjOGNkODkzOTExNDc3NzQyMjc0OWU0ZWQwYzI3OGI0ZmQyYzVjNmQzZCIsInRhZyI6IiJ9"
}
```

## 📊 Endpoints المتاحة

| Endpoint | الوصف |
|----------|--------|
| `dashboard` | لوحة التحكم الرئيسية |
| `users/table` | جدول المستخدمين |
| `users/online` | المستخدمين المتصلين |
| `users/statistics` | إحصائيات المستخدمين |
| `reports/daily` | التقارير اليومية |
| `reports/monthly` | التقارير الشهرية |
| `system/status` | حالة النظام |
| `system/logs` | سجلات النظام |
| `billing/summary` | ملخص الفواتير |
| `network/status` | حالة الشبكة |

## 🧪 أمثلة للاختبار

### 1. اختبار مع cURL:
```bash
# اختبار الاتصال
curl -X POST http://*************/api/sas4/test-connection \
  -H "Content-Type: application/json" \
  -d '{
    "sas4_ip": "*************",
    "username": "admin",
    "password": "admin123"
  }'

# الاتصال الكامل
curl -X POST http://*************/api/sas4/connect \
  -H "Content-Type: application/json" \
  -d '{
    "sas4_ip": "*************",
    "username": "admin",
    "password": "admin123",
    "endpoint": "dashboard"
  }'
```

### 2. اختبار مع JavaScript:
```javascript
// اختبار الاتصال
const testConnection = async () => {
    const response = await fetch('/api/sas4/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            sas4_ip: '*************',
            username: 'admin',
            password: 'admin123'
        })
    });
    
    const result = await response.json();
    console.log(result);
};

// الاتصال الكامل
const connectToSas4 = async () => {
    const response = await fetch('/api/sas4/connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            sas4_ip: '*************',
            username: 'admin',
            password: 'admin123',
            endpoint: 'users/table'
        })
    });
    
    const result = await response.json();
    console.log(result);
};
```

## 🔧 تخصيص التشفير

### إذا كنت تعرف مفاتيح SAS4 الحقيقية:

1. **حدث ملف .env:**
```env
SAS4_ENCRYPTION_KEY=your-actual-sas4-key-32-chars
SAS4_ENCRYPTION_IV=your-actual-iv-16
```

2. **أو حدث ملف config/sas4.php مباشرة:**
```php
'encryption' => [
    'method' => 'AES-256-CBC',
    'key' => 'your-actual-sas4-key-32-chars',
    'iv' => 'your-actual-iv-16',
],
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في التشفير:
- تأكد من صحة المفتاح والـ IV
- تأكد من أن طول المفتاح 32 حرف والـ IV 16 حرف

#### 2. فشل الاتصال:
- تأكد من صحة IP الخاص بلوحة SAS4
- تأكد من أن لوحة SAS4 تعمل على المنفذ الصحيح
- فحص إعدادات Firewall

#### 3. خطأ في المصادقة:
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من أن payload مشفر بالطريقة الصحيحة

### فحص Logs:
```bash
tail -f storage/logs/laravel.log
```

## 🎯 النتيجة النهائية

الآن يمكنك:
- ✅ الاتصال الديناميكي مع أي لوحة SAS4
- ✅ تشفير البيانات بنفس طريقة SAS4
- ✅ الحصول على token والوصول للبيانات
- ✅ جلب البيانات من endpoints مختلفة
- ✅ تخصيص إعدادات التشفير بسهولة

🎉 **النظام جاهز للاستخدام!**
