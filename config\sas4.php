<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SAS4 Dynamic Access Configuration
    |--------------------------------------------------------------------------
    |
    | إعدادات الاتصال الديناميكي مع لوحة SAS4
    |
    */

    // إعدادات التشفير
    'encryption' => [
        'method' => env('SAS4_ENCRYPTION_METHOD', 'AES-256-CBC'),
        'key' => env('SAS4_ENCRYPTION_KEY', 'your-32-character-secret-key-here'),
        'iv' => env('SAS4_ENCRYPTION_IV', 'your-16-char-iv'),
    ],

    // إعدادات الاتصال
    'connection' => [
        'timeout' => env('SAS4_CONNECTION_TIMEOUT', 30),
        'read_timeout' => env('SAS4_READ_TIMEOUT', 60),
        'max_retries' => env('SAS4_MAX_RETRIES', 3),
        'user_agent' => env('SAS4_USER_AGENT', 'AbrajiAPIs-SAS4-Client/1.0'),
    ],

    // Endpoints المتاحة
    'endpoints' => [
        'auth' => '/api/auth/login',
        'dashboard' => '/api/dashboard',
        'users_table' => '/api/users/table',
        'users_online' => '/api/users/online',
        'users_statistics' => '/api/users/statistics',
        'reports_daily' => '/api/reports/daily',
        'reports_monthly' => '/api/reports/monthly',
        'system_status' => '/api/system/status',
        'system_logs' => '/api/system/logs',
        'billing_summary' => '/api/billing/summary',
        'network_status' => '/api/network/status',
    ],

    // إعدادات الأمان
    'security' => [
        'allowed_ips' => env('SAS4_ALLOWED_IPS', ''), // فاصلة منقوطة للفصل
        'require_https' => env('SAS4_REQUIRE_HTTPS', false),
        'verify_ssl' => env('SAS4_VERIFY_SSL', true),
    ],

    // إعدادات التسجيل
    'logging' => [
        'enabled' => env('SAS4_LOGGING_ENABLED', true),
        'log_requests' => env('SAS4_LOG_REQUESTS', true),
        'log_responses' => env('SAS4_LOG_RESPONSES', false), // حساس
        'log_errors' => env('SAS4_LOG_ERRORS', true),
    ],

    // إعدادات Cache
    'cache' => [
        'enabled' => env('SAS4_CACHE_ENABLED', true),
        'ttl' => env('SAS4_CACHE_TTL', 300), // 5 دقائق
        'prefix' => env('SAS4_CACHE_PREFIX', 'sas4_'),
    ],

    // إعدادات افتراضية للاختبار
    'defaults' => [
        'test_ip' => env('SAS4_TEST_IP', '*************'),
        'test_username' => env('SAS4_TEST_USERNAME', 'admin'),
        'test_password' => env('SAS4_TEST_PASSWORD', 'admin123'),
        'test_endpoint' => env('SAS4_TEST_ENDPOINT', 'dashboard'),
    ],
];
