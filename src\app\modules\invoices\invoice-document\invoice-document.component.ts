import { Component } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { TranslocoModule } from '@jsverse/transloco';

@Component({
  selector: 'app-invoice-document',
  standalone: true,
  imports: [SharedModule,TranslocoModule],
  templateUrl: './invoice-document.component.html',
  styleUrl: './invoice-document.component.scss',
})
export class InvoiceDocumentComponent {
  currentDate: string;
  currentTime: string;

  constructor() {
    const now = new Date();
    this.currentDate = now.toLocaleDateString('ar-EG');
    this.currentTime = now.toLocaleTimeString('ar-EG');
  }
  printPage() {
    window.print();
  }
}
