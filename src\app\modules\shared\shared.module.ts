import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { SideBarComponent } from './side-bar/side-bar.component';
import { LogoComponent } from './logo/logo.component';
import { SharedRoutingModule } from './shared-routing.module';
import { TranslocoModule } from '@jsverse/transloco';
import { FlowbiteInitDirective } from './directives/flowbite-init.directive';
import { TimeAgoPipe } from './pips/time-ago.pipe';
import { BytesToSizePipe } from './pips/bytes-to-size.pipe';
import { ToastComponent } from './toast/toast.component';
import { StatsSkeletonComponent } from './skeletons/stats-skeleton/stats-skeleton.component';
import { LogoWordComponent } from './logo-word/logo-word.component';

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    SideBarComponent,
    LogoComponent,
    LogoWordComponent,
    FlowbiteInitDirective,
    TimeAgoPipe,
    BytesToSizePipe,
    ToastComponent,
    StatsSkeletonComponent,
  ],
  imports: [CommonModule, SharedRoutingModule, TranslocoModule],
  exports: [
    HeaderComponent,
    FooterComponent,
    SideBarComponent,
    LogoComponent,
    LogoWordComponent,
    FlowbiteInitDirective,
    TimeAgoPipe,
    BytesToSizePipe,
    ToastComponent,
    StatsSkeletonComponent,
  ],
})
export class SharedModule {}
