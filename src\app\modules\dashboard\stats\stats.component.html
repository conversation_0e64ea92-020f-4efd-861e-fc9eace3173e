<div>
  <div *ngIf="statistics">
    <div class="my-10 grid gap-y-10 gap-x-6 md:grid-cols-2 xl:grid-cols-4">
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-orange-600 to-orange-400 text-white shadow-orange-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd"
              d="M8 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4H6Zm7.25-2.095c.478-.86.75-1.85.75-2.905a5.973 5.973 0 0 0-.75-2.906 4 4 0 1 1 0 5.811ZM15.466 20c.34-.588.535-1.271.535-2v-1a5.978 5.978 0 0 0-1.528-4H18a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2h-4.535Z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="p-4">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.totalUsers' | transloco}}</p>
        </div>
        <div (click)="navigateToAllUsers()" class="cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.users_count }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-green-600 to-green-400 text-white shadow-green-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"
            class="w-6 h-6 text-white">
            <path
              d="M6.25 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM3.25 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM19.75 7.5a.75.75 0 00-1.5 0v2.25H16a.75.75 0 000 1.5h2.25v2.25a.75.75 0 001.5 0v-2.25H22a.75.75 0 000-1.5h-2.25V7.5z">
            </path>
          </svg>
        </div>
        <div class=" p-4 text-right">
          <p class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.activeUsers' | transloco}}</p>
        </div>
        <div (click)="navigateToActiveUsers()" class=" cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.active_users }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-blue-600 to-blue-400 text-white shadow-blue-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">

          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd"
              d="M17 10v1.126c.367.095.714.24 1.032.428l.796-.797 1.415 1.415-.797.796c.188.318.333.665.428 1.032H21v2h-1.126c-.095.367-.24.714-.428 1.032l.797.796-1.415 1.415-.796-.797a3.979 3.979 0 0 1-1.032.428V20h-2v-1.126a3.977 3.977 0 0 1-1.032-.428l-.796.797-1.415-1.415.797-.796A3.975 3.975 0 0 1 12.126 16H11v-2h1.126c.095-.367.24-.714.428-1.032l-.797-.796 1.415-1.415.796.797A3.977 3.977 0 0 1 15 11.126V10h2Zm.406 3.578.016.016c.354.358.574.85.578 1.392v.028a2 2 0 0 1-3.409 1.406l-.01-.012a2 2 0 0 1 2.826-2.83ZM5 8a4 4 0 1 1 7.938.703 7.029 7.029 0 0 0-3.235 3.235A4 4 0 0 1 5 8Zm4.29 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h6.101A6.979 6.979 0 0 1 9 15c0-.695.101-1.366.29-2Z"
              clip-rule="evenodd" />
          </svg>

        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.onlineUsers' | transloco}}</p>
        </div>
        <div (click)="navigateToOnlineUsers()" class="cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.online_users }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-rose-600 to-rose-400 text-white shadow-rose-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="currentColor" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M16 12h4M4 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
          </svg>

        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.expiredUsers' | transloco}}</p>
        </div>
        <div (click)="navigateToExpiredUsers()" class="cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.expired_users }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-violet-700 to-violet-400 text-white shadow-violet-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd"
              d="M5 5a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1 2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a2 2 0 0 1 2-2ZM3 19v-7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Zm6.01-6a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm-10 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end items-center antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.aboutExpire' | transloco}}
          </p>

        </div>
        <div (click)="navigateToExpiringSoonUsers()" class="cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.users_expire_in_3_days }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-lime-700 to-lime-400 text-white shadow-lime-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">

          <i class="pi pi-wallet" style="font-size: 1.4rem"></i>

        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.balance' | transloco}}</p>
        </div>
        <div class="border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.balance }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-pink-600 to-pink-400 text-white shadow-pink-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">

          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd"
              d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z"
              clip-rule="evenodd" />
          </svg>

        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.expireToday' | transloco}}</p>
        </div>
        <div (click)="navigateToExpiringTodayUsers()" class="cursor-pointer border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.expiring_today }}</h4>
        </div>
      </div>
      <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
        <div
          class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-sky-600 to-sky-400 text-white shadow-sky-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
          <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 9a3 3 0 0 1 3-3m-2 15h4m0-3c0-4.1 4-4.9 4-9A6 6 0 1 0 6 9c0 4 4 5 4 9h4Z" />
          </svg>
        </div>
        <div class="p-4 text-right">
          <p
            class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
            {{'dashboard.onlineFUP' | transloco}}</p>
        </div>
        <div class="border-t border-blue-gray-50 p-4">
          <h4
            class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
            {{ statistics!.fup_users }}</h4>
        </div>
      </div>

    </div>
  </div>
  <div *ngIf="transactions">
    <div *ngIf="transactions">
      <div class="my-12 grid gap-y-10 gap-x-6 md:grid-cols-2 xl:grid-cols-4">
        <div *ngIf="transactions.maintenance !== null" class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
          <div
            class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-yellow-600 to-yellow-400 text-white shadow-yellow-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
            <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 13v-2a1 1 0 0 0-1-1h-.757l-.707-1.707.535-.536a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0l-.536.535L14 4.757V4a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v.757l-1.707.707-.536-.535a1 1 0 0 0-1.414 0L4.929 6.343a1 1 0 0 0 0 1.414l.536.536L4.757 10H4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h.757l.707 1.707-.535.536a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l.536-.535 1.707.707V20a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-.757l1.707-.708.536.536a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-.535-.536.707-1.707H20a1 1 0 0 0 1-1Z" />
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
            </svg>

          </div>
          <div class="p-4 text-right">
            <p
              class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
              {{'dashboard.maintenance' | transloco}}</p>
          </div>
          <div class="border-t border-blue-gray-50 p-4">
            <h4
              class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
              {{ transactions!.maintenance.total_amount }} {{ 'common.iqd' | transloco }}</h4>
          </div>
        </div>
        <div *ngIf="transactions.expense !== null" class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
          <div
            class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tl from-green-600 to-green-400 text-white shadow-green-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
            <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd"
                d="M11 16.5a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0Zm4.5 2.5v-1.5H14v-2h1.5V14h2v1.5H19v2h-1.5V19h-2Z"
                clip-rule="evenodd" />
              <path d="M3.987 4A2 2 0 0 0 2 6v9a2 2 0 0 0 2 2h5v-2H4v-5h16V6a2 2 0 0 0-2-2H3.987Z" />
              <path fill-rule="evenodd" d="M5 12a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1Z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="p-4 text-right">
            <p
              class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
              {{'dashboard.cardsPurchase' | transloco}}</p>
          </div>
          <div class="border-t border-blue-gray-50 p-4">
            <h4
              class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
              {{ transactions!.expense.total_amount }} {{ 'common.iqd' | transloco }}</h4>
          </div>
        </div>
        <div *ngIf="transactions.general !== null" class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
          <div
            class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-br from-lime-600 to-lime-400 text-white shadow-lime-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
            <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
            </svg>

          </div>
          <div class="p-4 text-right">
            <h4
              class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
              {{'dashboard.generalExpenses' | transloco}}</h4>
          </div>
          <div class="border-t border-blue-gray-50 p-4">
            <h4
              class="flex justify-end antialiased tracking-normal font-sans text-2xl font-semibold leading-snug text-blue-gray-900">
              {{ transactions.general.total_amount }} {{ 'common.iqd' | transloco }}</h4>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="cards">
    <div class="my-12 grid gap-y-10 gap-x-6 md:grid-cols-2 xl:grid-cols-4">
      <div *ngFor="let card of cards">
        <div class="relative flex flex-col bg-clip-border rounded-xl bg-white text-gray-700 shadow-md">
          <div
            class="bg-clip-border mx-4 rounded-xl overflow-hidden bg-gradient-to-tr from-sky-600 to-sky-400 text-white shadow-sky-500/40 shadow-lg absolute -mt-4 grid h-16 w-16 place-items-center">
            <svg class="w-7 h-7 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd"
                d="M4 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H4Zm0 6h16v6H4v-6Z"
                clip-rule="evenodd" />
              <path fill-rule="evenodd"
                d="M5 14a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1Zm5 0a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1Z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="p-4 text-right">
            <p
              class="flex justify-end antialiased tracking-normal font-sans text-lg font-semibold leading-snug text-blue-gray-900">
              {{ 'dashboard.cardProfile' | transloco }} {{ card.profile }}
          </div>
          <div (click)="navigateToCardsProfile(card.id)" class="cursor-pointer border-t border-blue-gray-50 p-4">
            <div class="grid grid-cols-3 gap-3 mb-2">
              <dl class="bg-blue-50 dark:bg-gray-600 rounded-lg flex flex-col items-center justify-center h-[78px]">
                <dt
                  class="w-8 h-8 rounded-full bg-blue-100 dark:bg-gray-500 text-blue-600 dark:text-blue-300 text-sm font-medium flex items-center justify-center mb-1">
                  {{ card.total}}</dt>
                <dd class="text-blue-600 dark:text-blue-300 text-sm font-medium">{{ 'dashboard.total' | transloco }}
                </dd>
              </dl>
              <dl class="bg-orange-50 dark:bg-gray-600 rounded-lg flex flex-col items-center justify-center h-[78px]">
                <dt
                  class="w-8 h-8 rounded-full bg-orange-100 dark:bg-gray-500 text-orange-600 dark:text-orange-300 text-sm font-medium flex items-center justify-center mb-1">
                  {{ card.remaining }}</dt>
                <dd class="text-orange-600 dark:text-orange-300 text-sm font-medium">{{ 'dashboard.remaining' |
                  transloco }}</dd>
              </dl>
              <dl class="bg-teal-50 dark:bg-gray-600 rounded-lg flex flex-col items-center justify-center h-[78px]">
                <dt
                  class="w-8 h-8 rounded-full bg-teal-100 dark:bg-gray-500 text-teal-600 dark:text-teal-300 text-sm font-medium flex items-center justify-center mb-1">
                  {{ card.used }}</dt>
                <dd class="text-teal-600 dark:text-teal-300 text-sm font-medium">{{ 'dashboard.used' | transloco }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-container>
    <div class="grid grid-cols-1 gap-y-10 gap-x-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      <app-stats-skeleton *ngIf="!statistics" [count]="4"></app-stats-skeleton>
      <app-stats-skeleton *ngIf="!statistics" [count]="4"></app-stats-skeleton>
      <app-stats-skeleton *ngIf="!transactions" [count]="4"></app-stats-skeleton>
      <app-stats-skeleton *ngIf="!cards" [count]="4"></app-stats-skeleton>
    </div>
  </ng-container>
</div>
