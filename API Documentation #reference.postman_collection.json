{"info": {"_postman_id": "2df3861f-774c-451b-b52d-ec6de642fa1f", "name": "API Documentation #reference", "description": "This template contains a boilerplate for documentation that you can quickly customize and reuse.\n\n### How to use this template:\n\n- Replace the content given brackets (()) with your API's details.\n    \n- Tips are formatted in `codespan` - feel free to read and remove them.\n    \n\n``` javascript\nconst request = require(\"request\");\nconst CryptoJS = require(\"crypto-js\");\n//Login Form\nconst form1 = { username: \"azawi@hifi\", password: \"1111\" };\n// Users Table\nconst form2 = {\n  page: 1,\n  count: 10,\n  sortBy: \"username\",\n  direction: \"asc\",\n  search: \"\",\n  columns: [\n    \"idx\",\n    \"username\",\n    \"firstname\",\n    \"lastname\",\n    \"expiration\",\n    \"parent_username\",\n    \"name\",\n    \"loan_balance\",\n    \"traffic\",\n    \"remaining_days\",\n  ],\n};\n// online Users table\nconst form3 = {\n  page: 1,\n  count: 10,\n  sortBy: null,\n  direction: \"asc\",\n  search: \"\",\n  columns: [\n    \"id\",\n    \"username\",\n    \"acctoutputoctets\",\n    \"acctinputoctets\",\n    \"user_profile_name\",\n    \"framedipaddress\",\n    \"callingstationid\",\n    \"acctsessiontime\",\n    \"oui\",\n  ],\n};\n// user Filter\nconst form4 = {\n  page: 1,\n  count: 10,\n  sortBy: \"username\",\n  direction: \"asc\",\n  search: \"\",\n  columns: [\n    \"idx\",\n    \"username\",\n    \"firstname\",\n    \"lastname\",\n    \"expiration\",\n    \"parent_username\",\n    \"name\",\n    \"loan_balance\",\n    \"traffic\",\n    \"remaining_days\",\n  ],\n  status: 1,\n  connection: 2,\n  profile_id: 2,\n  parent_id: 7126,\n  group_id: -1,\n  site_id: -1,\n  sub_users: 1,\n  mac: \"\",\n};\n// user create\nconst form5 = {\n  username: \"azawi@hifi\",\n  enabled: 1,\n  password: \"1111\",\n  confirm_password: \"1111\",\n  profile_id: 2,\n  parent_id: 7126,\n  site_id: null,\n  mac_auth: 0,\n  allowed_macs: null,\n  use_separate_portal_password: 0,\n  portal_password: \"test1234\",\n  group_id: null,\n  firstname: \"test 12\",\n  lastname: \"tes\",\n  company: \"masr\",\n  email: \"<EMAIL>\",\n  phone: \"01015769576\",\n  city: \"SP\",\n  address: \"sad\",\n  apartment: null,\n  street: null,\n  contract_id: null,\n  national_id: null,\n  notes: null,\n  auto_renew: 0,\n};\n// issue invoice\nconst form6 = {\n  items: [\n    {\n      profile_id: \"2\",\n      qty: 1,\n      unit_price: 1,\n      total: 1,\n      name: \"NB 1\",\n      id: \"74aeb151-4872-d999-fc12-4fc4cf6ce3cc\",\n    },\n    {\n      qty: 12,\n      unit_price: 23,\n      total: 276,\n      name: \"test\",\n      id: \"88e15c4b-e72c-e14f-566f-207b12e123c9\",\n    },\n  ],\n  due_date: \"2024-06-25\",\n  client_id: \"324983\",\n  discount: 23,\n  total: 213.29,\n  comment: \"test comment\",\n};\n// get invoices table\nconst form7 = {\n  page: 1,\n  count: 10,\n  sortBy: \"created_at\",\n  direction: \"desc\",\n  search: \"\",\n  columns: [\n    \"invoice_number\",\n    \"due_date\",\n    \"username\",\n    \"type\",\n    \"amount\",\n    \"description\",\n    \"username\",\n    \"payment_method\",\n    \"paid\",\n  ],\n};\n// pay debit\nconst form8 = {\n  user_id: 319827,\n  username: \"mah\",\n  amount: 5,\n  comment: \"test comment\",\n  transaction_id: null,\n  is_loan: false,\n  debt_for_me: 16,\n  debt: 16,\n};\n// change profile (service)\nconst form9 = { user_id: \"319827\", profile_id: 2, change_type: \"immediate\" };\n// get user invoices\nconst form10 = {\n  page: 1,\n  count: 10,\n  sortBy: \"created_at\",\n  direction: \"desc\",\n  search: \"\",\n  columns: [\n    \"invoice_number\",\n    \"created_at\",\n    \"type\",\n    \"amount\",\n    \"description\",\n    \"username\",\n    \"payment_method\",\n    \"paid\",\n  ],\n};\n// activate user\nconst form11 = {\n  method: \"card\",\n  pin: \"32324322342343\",   // Card-Number\n  user_id: \"319827\",\n  money_collected: 1,\n  comments: \"test\",\n  user_price: \"100\",\n  issue_invoice: 1,\n  transaction_id: \"9d0cada1-4cf0-5985-c375-becc9c9e9784\",\n  activation_units: 1,\n};\nconst encryptionKey = \"abcdefghijuklmno0123456789012345\";\n// Encrypt the form data\nconst cypData = CryptoJS.AES.encrypt(\n  JSON.stringify(form11),\n  encryptionKey\n).toString();\nconsole.log(cypData);\n\n ```\n\n---\n\n`Start with a brief overview of what your API offers.`\n\nThe ((product name)) provides many API products, tools, and resources that enable you to ((add product value here)).\n\n`You can also list the APIs you offer, link to the relevant pages, or do both in this section.`\n\n## **Getting started guide**\n\n`List the steps or points required to start using your APIs. Make sure to cover everything required to reach success with your API as quickly as possible.`\n\nTo start using the ((add APIs here)), you need to -\n\n`The points given below are from The Postman API's documentation. You can reference it to write your own getting started guide.`\n\n- You must use a valid API Key to send requests to the API endpoints. You can get your API key from Postman's [integrations dashboard](https://go.postman.co/settings/me/api-keys).\n    \n- The API has [rate and usage limits](https://learning.postman.com/docs/developer/postman-api/postman-api-rate-limits/).\n    \n- The API only responds to HTTPS-secured communications. Any requests sent via HTTP return an HTTP 301 redirect to the corresponding HTTPS resources.\n    \n- The API returns request responses in JSON format. When an API request returns an error, it is sent in the JSON response as an error key.\n    \n\n## Authentication\n\n`Add details on the authorization keys/tokens required, steps that cover how to get them, and the relevant error codes.`\n\nThe ((product name)) API uses ((add your API's authorization type)) for authentication.\n\n`The details given below are from the Postman API's documentation. You can reference it to write your own authentication section.`\n\nPostman uses API keys for authentication. You can generate a Postman API key in the [API keys](https://postman.postman.co/settings/me/api-keys) section of your Postman account settings.\n\nYou must include an API key in each request to the Postman API with the X-Api-Key request header.\n\n### Authentication error response\n\nIf an API key is missing, malformed, or invalid, you will receive an HTTP 401 Unauthorized response code.\n\n## Rate and usage limits\n\n`Use this section to cover your APIs' terms of use. Include API limits, constraints, and relevant error codes, so consumers understand the permitted API usage and practices.`\n\n`The example given below is from The Postman API's documentation. Use it as a reference to write your APIs' terms of use.`\n\nAPI access rate limits apply at a per-API key basis in unit time. The limit is 300 requests per minute. Also, depending on your plan, you may have usage limits. If you exceed either limit, your request will return an HTTP 429 Too Many Requests status code.\n\nEach API response returns the following set of headers to help you identify your use status:\n\n| Header | Description |\n| --- | --- |\n| `X-RateLimit-Limit` | The maximum number of requests that the consumer is permitted to make per minute. |\n| `X-RateLimit-Remaining` | The number of requests remaining in the current rate limit window. |\n| `X-RateLimit-Reset` | The time at which the current rate limit window resets in UTC epoch seconds. |\n\n### 503 response\n\nAn HTTP `503` response from our servers indicates there is an unexpected spike in API access traffic. The server is usually operational within the next five minutes. If the outage persists or you receive any other form of an HTTP `5XX` error, [contact support](https://support.postman.com/hc/en-us/requests/new/).\n\n### **Need some help?**\n\n`Add links that customers can refer to whenever they need help.`\n\nIn case you have questions, go through our tutorials ((link to your video or help documentation here)). Or visit our FAQ page ((link to the relevant page)).\n\nOr you can check out our community forum, there’s a good chance our community has an answer for you. Visit our developer forum ((link to developer forum)) to review topics, ask questions, and learn from others.\n\n`You can also document or add links to libraries, code examples, and other resources needed to make a request.`", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "25069920", "_collection_link": "https://rootech-software.postman.co/workspace/Abraji-workspace~e3a993d0-1cdc-4a67-b943-5b1c9834c7e9/collection/25069920-2df3861f-774c-451b-b52d-ec6de642fa1f?action=share&source=collection_link&creator=25069920"}, "item": [{"name": "Authentication", "item": [{"name": "admin-login", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["// Extract the value of response['token'] and store it in an environment variable called 'access_token'\r", "pm.test(\"Extract and store the access token in an environment variable\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.environment.set(\"access_token\", jsonData.token);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\": \"U2FsdGVkX1/X7K5hNhqXio8TI4Gedwq/wY69YuG172YEIbTBWwGUq5N+HxVl6uj+Me3BXBsyr7EVzTjhrSFNKw==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/login", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "login"]}, "description": "This HTTP POST request is used to log in to the admin API. The request is sent to {{IP-or-Domain}}/admin/api/index.php/api/login.\n\nThe request body should contain a JSON payload with the username and password to be encrypted using CryptoJS. The user can use a pre-request script in Postman to dynamically encrypt the text and update the payload JSON with the encrypted text.\n\nUpon successful execution, the response will have a status code of 200 and a JSON content with the status and token.\n\nExample request body:\n\n``` json\n{\n  \"payload\": \"Sample text to be encrypted\"\n}\n\n ```\n\nExample response:\n\n``` json\n{\n  \"status\": 0,\n  \"token\": \"encrypted-token-value\"\n}\n\n ```"}, "response": []}]}, {"name": "Managers", "item": [{"name": "Manager-information", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Bearer {{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/index/manager", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "index", "manager"]}, "description": "This endpoint makes an HTTP GET request to retrieve information about the current authenticated manager it used to be added in user creation Form . The request does not contain a request body, and the response will include details about the manager, such as name, role, and contact information."}, "response": []}]}, {"name": "User", "item": [{"name": "Invoices", "item": [{"name": "issue-invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX18Dlj5Wutjdp4nDhsHFZJXE2/91V1i2RM69p/lCFLLT/l28geT+hBGB6cQa4N0q87tWH4rIv4LzuE+4l0aYDyFWKZGZIYo8rL/Nk/SCjroucEXxw+U4dFB487XwijTybYNOPQLoY3L7TrVSa4RJ7JzEr1X7EhZqVbMjruCrBZBZRzASUy0ZxGl/HCU3wfKtcJ8cH5Nb2gTeUz/hwqK/niZ5PnqipgHPO8VS9btoI+EtHTzNfhwVElg9AWdPSqkKuYAmGTxv5pRBCAEE/ffjZcviE03LRvMetEqIewsGeezurT2Zpr2YPQKKJTl91ytWTt8ua7RK8ITa+elv6AR8N+iIjVnOvHjOmrUvFZc0JE1X2Xc0Pltg4aDiJNPrcFHRckxsKfA0XwKQjy4uNtkQYCcNanvDYA7QcWh8QN1jLfszMR8FGmjMJZ/8\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/invoice", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "invoice"]}, "description": "## Add User Invoice\n\nThis endpoint allows you to add a new invoice for a user.\n\n### Request Body\n\n- `payload` (string, required): The payload containing the details of the invoice to be added.\n    \n\n``` json\n// issue invoice\nconst form6 = {\n  items: [\n    {\n      profile_id: \"2\",\n      qty: 1,\n      unit_price: 1,\n      total: 1,\n      name: \"NB 1\",\n      id: \"74aeb151-4872-d999-fc12-4fc4cf6ce3cc\",\n    },\n    {\n      qty: 12,\n      unit_price: 23,\n      total: 276,\n      name: \"test\",\n      id: \"88e15c4b-e72c-e14f-566f-207b12e123c9\",\n    },\n  ],\n  due_date: \"2024-06-25\",\n  client_id: \"324983\",\n  discount: 23,\n  total: 213.29,\n  comment: \"test comment\",\n};\n\n ```\n\n### Response\n\nThe response will contain the status of the request to add the user invoice."}, "response": []}, {"name": "users-invoices", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/index/UserInvoices", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "index", "UserInvoices"]}, "description": "### Add User Invoices\n\nThis endpoint allows the admin to add user invoices.\n\n#### Request Body\n\n- payload (string, required): The payload for adding user invoices.\n    \n\n``` json\n// get invoices table\nconst form7 = {\n  page: 1,\n  count: 10,\n  sortBy: \"created_at\",\n  direction: \"desc\",\n  search: \"\",\n  columns: [\n    \"invoice_number\",\n    \"due_date\",\n    \"username\",\n    \"type\",\n    \"amount\",\n    \"description\",\n    \"username\",\n    \"payment_method\",\n    \"paid\",\n  ],\n};\n\n ```\n\nExample:\n\n``` json\n{\n  \"payload\": \"...\"\n}\n\n ```\n\n#### Response\n\nThe response will include the result of the user invoice addition operation."}, "response": []}, {"name": "user-invoices", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX18sc4+Hn8A11TpImtXaywWKozI81809gXUxw5a3b9cnfdb7N+PuhFUDojCz73wn8SgwUSV0dsKNZs9dJ8+UHwfpyQ5IIjAoiRu1J3RdAv/PM4KUzh8aTnKl/R9sjyXIuYBLdurcmpg40tiwaBWzMv8L5veFfJD41Tun9IE5gWWhbrh9i0mYrpfcO66w6nyDJxfCBWmBUnDIyNSpgL0Q54NS/Uft9y6yHQlmOl01WHsntsTqQ7HqSPC1r9z3hgubqFWA98+J7ENvgA==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/index/UserInvoices/319827", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "index", "UserInvoices", "319827"]}, "description": "The HTTP POST request to {{IP-or-Domain}}/admin/api/index.php/api/index/UserInvoices/319827 is used to retrieve user invoices data.\n\n### Request Body\n\n- The request payload is expected to be in raw format with an empty string as the payload.\n    \n\n``` json\n// get user invoices\nconst form10 = {\n  page: 1,\n  count: 10,\n  sortBy: \"created_at\",\n  direction: \"desc\",\n  search: \"\",\n  columns: [\n    \"invoice_number\",\n    \"created_at\",\n    \"type\",\n    \"amount\",\n    \"description\",\n    \"username\",\n    \"payment_method\",\n    \"paid\",\n  ],\n};\n\n ```\n\n### Response\n\nThe response returns a JSON object with the following schema:\n\n``` json\n{\n  \"current_page\": number,\n  \"data\": [\n    {\n      \"id\": number,\n      \"invoice_number\": \"string\",\n      \"due_date\": \"string\",\n      \"type\": \"string\",\n      \"amount\": \"string\",\n      \"user_id\": number,\n      \"description\": \"string or null\",\n      \"paid\": number,\n      \"created_by\": number,\n      \"created_at\": \"string\",\n      \"payment_method\": \"string or null\",\n      \"manager_details\": {\n        \"id\": number,\n        \"username\": \"string\",\n        \"firstname\": \"string\",\n        \"lastname\": \"string\"\n      },\n      \"user_details\": {\n        \"id\": number,\n        \"username\": \"string\",\n        \"parent_username\": \"string or null\"\n      }\n    }\n  ],\n  \"first_page_url\": \"string\",\n  \"from\": number,\n  \"last_page\": number,\n  \"last_page_url\": \"string\",\n  \"next_page_url\": \"string or null\",\n  \"path\": \"string\",\n  \"per_page\": number,\n  \"prev_page_url\": \"string or null\",\n  \"to\": number,\n  \"total\": number\n}\n\n ```\n\nThe response contains an array of user invoices with details such as ID, invoice number, due date, amount, user details, and manager details. The pagination information is also included in the response."}, "response": []}, {"name": "invoice", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/userInvoice/32", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "userInvoice", "32"]}, "description": "This API endpoint retrieves the user invoice details for a specific user ID. The response is in JSON format and includes the status of the request along with detailed invoice information and user details.\n\n``` json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"status\": { \"type\": \"number\" },\n    \"data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"id\": { \"type\": \"number\" },\n        \"invoice_number\": { \"type\": \"string\" },\n        \"user_id\": { \"type\": \"number\" },\n        \"type\": { \"type\": \"string\" },\n        \"amount\": { \"type\": \"string\" },\n        \"due_date\": { \"type\": \"string\" },\n        \"description\": { \"type\": [\"string\", \"null\"] },\n        \"paid\": { \"type\": \"number\" },\n        \"paid_on\": { \"type\": [\"string\", \"null\"] },\n        \"vat\": { \"type\": [\"number\", \"null\"] },\n        \"value\": { \"type\": \"string\" },\n        \"payment_method\": { \"type\": [\"string\", \"null\"] },\n        \"data\": { \"type\": [\"string\", \"null\"] },\n        \"qty\": { \"type\": [\"number\", \"null\"] },\n        \"discount\": { \"type\": \"number\" },\n        \"comment\": { \"type\": [\"string\", \"null\"] },\n        \"unit_price\": { \"type\": [\"string\", \"null\"] },\n        \"created_by\": { \"type\": \"number\" },\n        \"created_at\": { \"type\": \"string\" },\n        \"updated_at\": { \"type\": \"string\" },\n        \"items\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"id\": { \"type\": \"number\" },\n              \"invoice_id\": { \"type\": \"number\" },\n              \"name\": { \"type\": \"string\" },\n              \"qty\": { \"type\": \"number\" },\n              \"unit_price\": { \"type\": \"string\" },\n              \"tax\": { \"type\": [\"number\", \"null\"] },\n              \"type\": { \"type\": [\"string\", \"null\"] },\n              \"updated_at\": { \"type\": \"string\" },\n              \"deleted_at\": { \"type\": [\"string\", \"null\"] },\n              \"created_at\": { \"type\": \"string\" }\n            }\n          }\n        },\n        \"user\": {\n          \"type\": \"object\",\n          \"properties\": {\n            // Properties for user details, similar to the above structure\n          }\n        }\n      }\n    }\n  }\n}\n\n ```"}, "response": []}, {"name": "debt", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/debt/318820", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "debt", "318820"]}, "description": "The endpoint retrieves the debt information for a specific user with the ID 318820.\n\nThe response of the request can be documented as a JSON schema:\n\n``` json\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"status\": {\n            \"type\": \"integer\"\n        },\n        \"data\": {\n            \"type\": \"object\",\n            \"properties\": {\n                \"total\": {\n                    \"type\": \"integer\"\n                },\n                \"debt_for_me\": {\n                    \"type\": \"integer\"\n                },\n                \"username\": {\n                    \"type\": \"string\"\n                },\n                \"balance\": {\n                    \"type\": \"integer\"\n                }\n            }\n        }\n    }\n}\n\n ```"}, "response": []}, {"name": "payDebit", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\": \"U2FsdGVkX1+DcU9T+cOqh4OBT64qDvyQbW6Rlt9ZhMaTK2PN/ZbZQqbTyFl/CN9bslN0ODcz+j6BA5n4yBN9Kmgr9uZJAVF8F32lTvHhriJZNz5uhDlUDnrgxdAZ6+gkEosi/UjE00Ay8VsPUom0uiz3CQGorL+52yp9aSYsWalppqrxXpSsoEnkGMWQ99s1Z/SXY5YFbRwReNNKq4hQYw==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/payDebt", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "payDebt"]}, "description": "This endpoint allows administrators to initiate a payment to clear a user's debt.\n\n### Request Body\n\n- `payload` (string): The payload should be provided in the request body.\n    \n\n``` json\n// pay debit\nconst form8 = {\n  user_id: 319827,\n  username: \"mah\",\n  amount: 5,\n  comment: \"test comment\",\n  transaction_id: null,\n  is_loan: false,\n  debt_for_me: 16,\n  debt: 16,\n};\n\n ```\n\n### Response\n\nUpon a successful execution, the response will have a status code of 200 and a JSON object with the following fields:\n\n- `status` (integer): Indicates the status of the operation (0 for success).\n    \n- `message` (string): A message related to the operation."}, "response": []}, {"name": "Download-invoice", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/userInvoice/download/25", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "userInvoice", "download", "25"]}, "description": "This endpoint makes an HTTP GET request to download a user invoice with the ID 33. The response will contain the user invoice in PDF format.\n\nThe request does not include a request body, as it is a simple GET request to download the invoice.\n\nThe response will have a status code of 200, indicating a successful request, and the content type will be \"application/pdf\", confirming that the response will contain the user invoice in PDF format."}, "response": []}]}, {"name": "Table", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\":\"U2FsdGVkX19xqrFqpc+X28R/aEdG8ed5YGdd5sMurDadlVW/a9hTCO5YfAJa77C6HW2sv3UXRH/7kz6JEO6sNnP5IcrNmm7OgUnnZSe4vjhQ+tOLxzkRq5rFkJefvjPCNGzDJur8zTVFUsKqZVieXJbID6YASdxSeYfVa6B7BCJSQLur/We2SZKF1GFjvLQyzBU+v17Q9nDoWnfi/n6CN1KZkFHut+YsvGrNXHpX2Q8aew+kVXn051gm0Rn0tKVyrqT8DZAb/fiIm8OLtGu6GJe1WZQG1sVGvaw+cOuocZQ=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/index/user", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "index", "user"]}, "description": "This endpoint allows you to Fetch user table by making an HTTP POST request to {{IP-or-Domain}}/admin/api/index.php/api/index/user.\n\nthe api can be used to ( filter , search , show/hide Columns ,) also\n\n### Request Body\n\nThe request should include a payload in the raw request body type. The payload should be a JSON object with the necessary user table information.\n\n``` json\nform = { page: 1, count: 10, sortBy: \"username\", direction: \"asc\", search: \"\", columns: \\[ \"idx\", \"username\", \"firstname\", \"lastname\", \"expiration\", \"parent_username\", \"name\", \"loan_balance\", \"traffic\", \"remaining_days\", \\],};\n\n ```\n\n### Response\n\nUpon successful execution, the API will respond with a status code of 200 and a JSON object containing the details of the newly created user, including their ID, username, first name, last name, city, phone, balance, and other relevant information.\n\nExample Response:\n\n``` json\n{\n    \"current_page\": 0,\n    \"data\": [\n        {\n            \"id\": 0,\n            \"username\": \"\",\n            \"firstname\": \"\",\n            \"lastname\": null,\n            \"city\": null,\n            \"phone\": null,\n            \"profile_id\": 0,\n            \"balance\": \"\",\n            \"loan_balance\": \"\",\n            \"expiration\": \"\",\n            \"last_online\": null,\n            \"parent_id\": 0,\n            \"email\": null,\n            \"static_ip\": null,\n            \"enabled\": 0,\n            \"company\": null,\n            \"notes\": null,\n            \"simultaneous_sessions\": 0,\n            \"address\": null,\n            \"contract_id\": null,\n            \"created_at\": \"\",\n            \"national_id\": null,\n            \"mikrotik_ipv6_prefix\": null,\n            \"group_id\": 0,\n            \"gps_lat\": null,\n            \"gps_lng\": null,\n            \"street\": null,\n            \"n_row\": 0,\n            \"remaining_days\": 0,\n            \"status\": {\n                \"status\": true,\n                \"traffic\": true,\n                \"expiration\": true,\n                \"uptime\": true\n            },\n            \"online_status\": 0,\n            \"parent_username\": \"\",\n            \"profile_details\": {\n                \"id\": 0,\n                \"name\": \"\",\n                \"type\": 0\n            },\n            \"daily_traffic_details\": null,\n            \"group_details\": {\n                \"id\": 0,\n                \"group_name\": \"\"\n            }\n        }\n    ],\n    \"first_page_url\": \"\",\n    \"from\": 0,\n    \"last_page\": 0,\n    \"last_page_url\": \"\",\n    \"next_page_url\": null,\n    \"path\": \"\",\n    \"per_page\": 0,\n    \"prev_page_url\": null,\n    \"to\": 0,\n    \"total\": 0\n}\n\n ```"}, "response": []}, {"name": "Online-users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19JFZr6cR/ChICwOqKBCmyTJlgz4uAMV7WalaAlhaO6SKib922yltwKWu8e6B6waYbOKkPVYuCmTIA3BBhTShHCktsv4S/k/UgVN2zVtGoG7kdfxce+mbRbvEYOQegm3ECN+cyKjQb9DO0jXDYZ+So1zC4GDQ2oGvSlb1NV0g/hsqRhmV9rdI7YnFKO6ur+GZmVFHl0tDcKW9F/fP4aeMd258N5QQTQF/NK//f0N10WiX9ju6W9ybQf1uMRC9Wa5X87F2jvtcNs6Pp/Tr0C4uwpc59BQR/NqI9HGwZKGVKaNto818ClIHt3\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/index/online", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "index", "online"]}, "description": "### Online Index API\n\nThis API endpoint is used to retrieve online index data.\n\n#### Request Body\n\n- Payload (text): The payload parameter should be included in the request body as a JSON string.\n    \n\n``` json\nform3 = { page: 1, count: 10, sortBy: null, direction: \"asc\", search: \"\", columns: \\[ \"id\", \"username\", \"acctoutputoctets\", \"acctinputoctets\", \"user_profile_name\", \"framedipaddress\", \"callingstationid\", \"acctsessiontime\", \"oui\", \\], };\n\n ```\n\n#### Response\n\nThe response will be in JSON format with the following schema:\n\n``` json\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"current_page\": {\"type\": \"number\"},\n        \"data\": {\"type\": \"array\"},\n        \"first_page_url\": {\"type\": \"string\"},\n        \"from\": {\"type\": [\"number\", \"null\"]},\n        \"last_page\": {\"type\": \"number\"},\n        \"last_page_url\": {\"type\": \"string\"},\n        \"next_page_url\": {\"type\": [\"string\", \"null\"]},\n        \"path\": {\"type\": \"string\"},\n        \"per_page\": {\"type\": \"number\"},\n        \"prev_page_url\": {\"type\": [\"string\", \"null\"]},\n        \"to\": {\"type\": [\"number\", \"null\"]},\n        \"total\": {\"type\": \"number\"}\n    }\n}\n\n ```"}, "response": []}, {"name": "user-create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/ZzryWOO9HJNQjen3aYP3AvOtDEb+kiKe0m+gswU3XI3Uvyph7qTC4VpRtAM/7iBcjXaA4r0f/IvGYjfyUfzkfXuZIeMWULwH3LMRNv2rJQH66ZrnNDMTCPDMkCsNCZbDQAmE1PQCRfCKxiPzWbw83ZE5ifg6Mcli8fahL9X4ep1VFP8wE4LUE5eKRb18jLFLDLzbnJcpy5zg0ZOnCrL+j1hmI/crsuSEgmQjGj8hcV9RKt6LLrjD+WQI86mtF6eFU2FS35xAy7HXSZfL5JdLLfbDWKB+qJB3H+Q15deOZaV6BzNwRnMcMTh1AvIS9Vcl0+s8+JIYgP2R9dHhmKl+dVmA4ExHFPhOkJJEnUS6FdlJHmDvylgGNL1QBFze3wl+3puYuI2q8QI99d4BI1Q+HMQwfZ0zJEv8aKgdpLro9iRfX5jbT4u1hYnBVpZshtouFW91W3u0kpaHDIV+6a9aYo32vxBj+ZEqqlcFXFwzH499egv18YgZjfnVYiGhBA40slZQpmQPj8aE8HWWReBCOtY1oTpDuWPDwtTihkHlECh3oDkganJMUPn6LDZKQVDH6VQyWm2dTQZi0EexfeAPmztwmfU7T2iEG7mCXIaD23XBejX3RbeZ0MYgXFW1pFWBKc1ra8RYqHg==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user"]}, "description": "### Create User API\n\nThis API endpoint is used to create a new user.\n\n#### Request Body\n\n- payload (string, required): The payload should contain the details of the user to be created.\n    \n- payload decrypted data\n    \n    ``` json\n      form5 = {\n      username: \"azawi@hifi\",\n      enabled: 1,\n      password: \"1111\",\n      confirm_password: \"1111\",\n      profile_id: 2,\n      parent_id: 7126,\n      site_id: null,\n      mac_auth: 0,\n      allowed_macs: null,\n      use_separate_portal_password: 0,\n      portal_password: \"test1234\",\n      group_id: null,\n      firstname: \"test 12\",\n      lastname: \"tes\",\n      company: \"masr\",\n      email: \"<EMAIL>\",\n      phone: \"01015769576\",\n      city: \"SP\",\n      address: \"sad\",\n      apartment: null,\n      street: null,\n      contract_id: null,\n      national_id: null,\n      notes: null,\n      auto_renew: 0,\n      };\n    \n     ```\n    \n\n#### Response\n\nThe response of this request is a JSON schema representing the structure of the response data.\n\n``` json\n{\n    \"status\": 200,\n    \"message\": \"rsp_save_success\",\n    \"data\": {\n        \"username\": \"azawi@hifi\",\n        \"enabled\": 1,\n        \"profile_id\": 2,\n        \"parent_id\": 7126,\n        \"site_id\": null,\n        \"mac_auth\": 0,\n        \"use_separate_portal_password\": 0,\n        \"group_id\": 1,\n        \"firstname\": \"test 12\",\n        \"lastname\": \"tes\",\n        \"company\": \"masr\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"01015769576\",\n        \"city\": \"SP\",\n        \"address\": \"sad\",\n        \"apartment\": null,\n        \"street\": null,\n        \"contract_id\": null,\n        \"national_id\": null,\n        \"notes\": null,\n        \"auto_renew\": 0,\n        \"created_by\": 7126,\n        \"expiration\": {\n            \"date\": \"2024-06-25 16:29:36.324855\",\n            \"timezone_type\": 3,\n            \"timezone\": \"Asia\\/Baghdad\"\n        },\n        \"updated_at\": \"2024-06-25 16:29:36\",\n        \"created_at\": \"2024-06-25 16:29:36\",\n        \"id\": 324983,\n        \"parent_username\": \"azawi@hifi\"\n    }\n}\n\n ```"}, "response": []}, {"name": "UserById", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/318820", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "318820"]}, "description": "This endpoint makes an HTTP GET request to retrieve information about a specific user with the ID. The response of this request is documented as a JSON schema."}, "response": []}, {"name": "user-edit", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/ZzryWOO9HJNQjen3aYP3AvOtDEb+kiKe0m+gswU3XI3Uvyph7qTC4VpRtAM/7iBcjXaA4r0f/IvGYjfyUfzkfXuZIeMWULwH3LMRNv2rJQH66ZrnNDMTCPDMkCsNCZbDQAmE1PQCRfCKxiPzWbw83ZE5ifg6Mcli8fahL9X4ep1VFP8wE4LUE5eKRb18jLFLDLzbnJcpy5zg0ZOnCrL+j1hmI/crsuSEgmQjGj8hcV9RKt6LLrjD+WQI86mtF6eFU2FS35xAy7HXSZfL5JdLLfbDWKB+qJB3H+Q15deOZaV6BzNwRnMcMTh1AvIS9Vcl0+s8+JIYgP2R9dHhmKl+dVmA4ExHFPhOkJJEnUS6FdlJHmDvylgGNL1QBFze3wl+3puYuI2q8QI99d4BI1Q+HMQwfZ0zJEv8aKgdpLro9iRfX5jbT4u1hYnBVpZshtouFW91W3u0kpaHDIV+6a9aYo32vxBj+ZEqqlcFXFwzH499egv18YgZjfnVYiGhBA40slZQpmQPj8aE8HWWReBCOtY1oTpDuWPDwtTihkHlECh3oDkganJMUPn6LDZKQVDH6VQyWm2dTQZi0EexfeAPmztwmfU7T2iEG7mCXIaD23XBejX3RbeZ0MYgXFW1pFWBKc1ra8RYqHg==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/324983", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "324983"]}, "description": "### Update User Details\n\nThis endpoint allows the admin to update the details of a specific user.\n\n#### Request Body\n\n- payload (string, required): The payload for updating the user details.\n    \n- payload data is the same as create\n    \n\n#### Response\n\nThe response is in JSON format with the following schema:\n\n``` json\n{\n    \"status\": 0\n}\n\n ```\n\nThe `status` field indicates the status of the update operation."}, "response": []}, {"name": "Disconnect-user", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/disconnect/userid/324983", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "disconnect", "userid", "324983"]}, "description": "### Get User Disconnect\n\nThis endpoint makes an HTTP GET request to disconnect a specific user by their user ID.\n\n#### Request\n\n- Method: GET\n    \n- URL: `{{IP-or-Domain}}/admin/api/index.php/api/user/disconnect/userid/324983`\n    \n\n#### Response\n\n- Status: 200\n    \n- Content-Type: application/json\n    \n\n``` json\n{\n    \"status\": 0,\n    \"message\": \"\"\n}\n\n ```"}, "response": []}, {"name": "activate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/nkDD8y4J0Tc4bJ9gdCMJo+JSuZNP/lKuLh8/QMx3CMgWNjQ2BzkcVaAiiC5AkiJ0N172rct4wsgIkhHSZGcp+wyGoonU8KB41woFymmjtikCPV5WE36tmmchtMb2rl2ebV+****************************/woVBDHYog8+88r/bXQwAfyGVl4JviQv/KC+/nSVcngT2EB0ShjN/YSKbzm6v8wI9IPkFhtewp8DTvjm4CKEZYmvDhXmbxdFbD55no4X6FE0oBfgdZ7+w8a3dbHwFidMbhat2b190Ws7y8HWZMnfct\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/activate", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "activate"]}, "description": "The Post request to `/admin/api/index.php/api/user/activate` is used to activate a user.\n\n``` json\n// activate user\nconst form11 = {\n  method: \"card\",\n  pin: \"32324322342343\",   // Card-Number\n  user_id: \"319827\",\n  money_collected: 1,\n  comments: \"test\",\n  user_price: \"100\",\n  issue_invoice: 0,\n  transaction_id: \"9d0cada1-4cf0-5985-c375-becc9c9e9784\",\n  activation_units: 1,\n};\n\n ```\n\n### Response\n\nThe request returns a `403 Forbidden` status with a `Content-Type` of `text/html`. The response body contains an HTML document with the title \"Forbidden\" and a message \"403 Forbidden\".\n\n``` json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"html\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"lang\": {\n          \"type\": \"string\"\n        },\n        \"head\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"charset\": {\n              \"type\": \"string\"\n            },\n            \"viewport\": {\n              \"type\": \"string\"\n            },\n            \"title\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"body\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"flex-center\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"code\": {\n                  \"type\": \"string\"\n                },\n                \"message\": {\n                  \"type\": \"string\"\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n ```"}, "response": []}], "description": "The `/me` endpoints let you manage information about the authenticated user.", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Dashboard", "item": [{"name": "Dashboard-home", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/dashboard", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "dashboard"]}, "description": "### Get Dashboard Data\n\nThis endpoint retrieves the dashboard data for the admin.\n\n#### Request\n\n- Method: GET\n    \n- URL: `{{IP-or-Domain}}/admin/api/index.php/api/dashboard`\n    \n\n#### Response\n\n- Status: 200\n    \n- Content-Type: application/json\n    \n- Body:\n    \n    ``` json\n      {\n        \"status\": 0,\n        \"data\": {\n          \"widgets\": [\n            {\n              \"row_id\": \"\",\n              \"row_order\": 0,\n              \"widgets\": [\n                {\n                  \"id\": 0,\n                  \"name\": \"\",\n                  \"type\": \"\",\n                  \"color\": \"\",\n                  \"width\": \"\",\n                  \"icon\": \"\",\n                  \"external_data_source\": null,\n                  \"internal_data_source\": \"\",\n                  \"data_source\": \"\",\n                  \"title\": \"\",\n                  \"interval\": 0,\n                  \"description\": \"\",\n                  \"built_in\": 0,\n                  \"url\": \"\",\n                  \"created_at\": \"\",\n                  \"updated_at\": \"\",\n                  \"created_by\": null\n                }\n              ]\n            }\n          ],\n          \"alert\": {\n            \"enabled\": true,\n            \"type\": null,\n            \"text\": null\n          },\n          \"id\": 0\n        }\n      }\n    \n     ```"}, "response": []}]}, {"name": "Service Profile", "item": [{"name": "Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/list/profile/0", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "list", "profile", "0"]}, "description": "### GET /admin/api/index.php/api/list/profile/0\n\nThis endpoint retrieves a list of profiles with the specified ID.\n\n#### Request\n\nNo request body is required for this endpoint.\n\n- `IP-or-Domain` (string) - The IP address or domain of the server.\n    \n\n#### Response\n\nThe response will be in JSON format with the following schema:\n\n``` json\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"0\": {\n            \"type\": \"number\"\n        },\n        \"status\": {\n            \"type\": \"number\"\n        },\n        \"data\": {\n            \"type\": \"array\",\n            \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                    \"id\": {\n                        \"type\": \"number\"\n                    },\n                    \"name\": {\n                        \"type\": \"string\"\n                    }\n                }\n            }\n        }\n    }\n}\n\n ```\n\nThe response will contain:\n\n- `0` (number) - The value associated with key 0.\n    \n- `status` (number) - The status code of the response.\n    \n- `data` (array) - An array of profile objects, each containing:\n    - `id` (number) - The ID of the profile.\n        \n    - `name` (string) - The name of the profile."}, "response": []}, {"name": "change-user-service", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19TfcQek1eBCGJaE0e31zTaCzvIZe/hOLdSUP8wV5Cklo5BPbwlxOUq11arti39DtUYoKwQcRr8PvgXzpWOLiuJhkGw/jNm7ek=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/user/changeProfile", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "user", "changeProfile"]}, "description": "This API endpoint allows administrators to change the profile of a user. The HTTP POST request should be sent to {{IP-or-Domain}}/admin/api/index.php/api/user/changeProfile.\n\n### Request Body\n\n- payload (string, required): The payload should be provided in the request body.\n    \n\n``` json\n// change profile (service)\nconst form9 = { user_id: \"319827\", profile_id: 2, change_type: \"immediate\" };\n\n ```\n\n### Response\n\nThe response of this request is documented as a JSON schema.\n\n{\"status\":200,\"message\":\"rsp_profile_changed\"}"}, "response": []}]}, {"name": "Resources", "item": [{"name": "Side-<PERSON>u", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/resources/menu", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "resources", "menu"]}, "description": "This endpoint makes an HTTP GET request to retrieve the menu resources from the admin API. The response of this request is documented as a JSON schema below:\n\n``` json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"menuItems\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"string\"\n          },\n          \"name\": {\n            \"type\": \"string\"\n          },\n          \"url\": {\n            \"type\": \"string\"\n          },\n          \"icon\": {\n            \"type\": \"string\"\n          }\n        }\n      }\n    }\n  }\n}\n\n ```"}, "response": []}, {"name": "Language", "request": {"method": "GET", "header": [], "url": {"raw": "{{IP-or-Domain}}/admin/api/index.php/api/resources/languages", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "resources", "languages"]}, "description": "The `GET` request retrieves a list of languages available in the admin API resources. The response is a JSON object with a `status` property indicating the success status, and a `data` array containing language objects with properties `id`, `name`, `direction`, `author`, and `font`. Below is the JSON schema for the response:\n\n``` json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"status\": {\n      \"type\": \"number\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"string\"\n          },\n          \"name\": {\n            \"type\": \"string\"\n          },\n          \"direction\": {\n            \"type\": \"string\"\n          },\n          \"author\": {\n            \"type\": \"string\"\n          },\n          \"font\": {\n            \"type\": \"string\"\n          }\n        }\n      }\n    }\n  }\n}\n\n ```"}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vault:access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = pm.response.json();\r", "pm.environment.set(\"access_token\", jsonData.token);"]}}], "variable": [{"key": "IP-or-Domain", "value": "https://sas.nbtel.iq"}, {"key": "access_token", "value": " "}]}