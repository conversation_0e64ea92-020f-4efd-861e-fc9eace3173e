import { Component } from '@angular/core';
import { AllUsersComponent } from '../all-users/all-users.component';
import { initialOnlineUsersColumnsState } from '../../../core/user-services/api/users';

@Component({
  selector: 'app-online-users',
  templateUrl: '../all-users/all-users.component.html',
  styleUrls: ['../all-users/all-users.component.scss']
})
export class OnlineUsersComponent extends AllUsersComponent {
  override ngOnInit(): void {
    // initiate columns state
    this.userColumnsState = this.localStorageService
      .loadColumnsState(this.localStorageService.OnlineUserColumnsState)
      || initialOnlineUsersColumnsState;

    this.userForm = {
      page: 1,
      count: 10,
      sortBy: "",
      direction: "asc",
      search: "",
      columns: this.getVisibleColumns(),
    };

    // fetch users
    this.fetchUsers();
  }
  // override fetchUsers
  override fetchUsers(): void {

    this.isLoading = true;
    this.userService.getOnlineUsers(this.userForm).subscribe({
      next: (response: any) => {
        this.tableResponse = response;
        console.log("online response: ", response);

      },
      complete: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        console.error(error);
        this.toastService.addToast('error', 'Error loading users', 'An error occurred while loading the users.');
      },
    });
  }

  override toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(columnKey, this.userColumnsState, this.localStorageService.OnlineUserColumnsState);
  }

}
