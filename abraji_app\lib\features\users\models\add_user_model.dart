import 'user_data_model.dart';

class AddUserRequest {
  final String username;
  final String password;
  final String? fullName;
  final String? email;
  final String? phone;
  final String? address;
  final String status;
  final String? plan;
  final DateTime? expiryDate;
  final double? balance;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  AddUserRequest({
    required this.username,
    required this.password,
    this.fullName,
    this.email,
    this.phone,
    this.address,
    this.status = 'active',
    this.plan,
    this.expiryDate,
    this.balance,
    this.notes,
    this.additionalData,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'username': username,
      'password': password,
      'status': status,
    };

    if (fullName != null && fullName!.isNotEmpty) {
      data['full_name'] = fullName;
    }
    if (email != null && email!.isNotEmpty) {
      data['email'] = email;
    }
    if (phone != null && phone!.isNotEmpty) {
      data['phone'] = phone;
    }
    if (address != null && address!.isNotEmpty) {
      data['address'] = address;
    }
    if (plan != null && plan!.isNotEmpty) {
      data['plan'] = plan;
    }
    if (expiryDate != null) {
      data['expiry_date'] = expiryDate!.toIso8601String();
    }
    if (balance != null) {
      data['balance'] = balance;
    }
    if (notes != null && notes!.isNotEmpty) {
      data['notes'] = notes;
    }
    if (additionalData != null) {
      data['additional_data'] = additionalData;
    }

    return data;
  }

  AddUserRequest copyWith({
    String? username,
    String? password,
    String? fullName,
    String? email,
    String? phone,
    String? address,
    String? status,
    String? plan,
    DateTime? expiryDate,
    double? balance,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return AddUserRequest(
      username: username ?? this.username,
      password: password ?? this.password,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      status: status ?? this.status,
      plan: plan ?? this.plan,
      expiryDate: expiryDate ?? this.expiryDate,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}

class UpdateUserRequest {
  final String? fullName;
  final String? email;
  final String? phone;
  final String? address;
  final String? status;
  final String? plan;
  final DateTime? expiryDate;
  final double? balance;
  final String? notes;
  final String? password;
  final Map<String, dynamic>? additionalData;

  UpdateUserRequest({
    this.fullName,
    this.email,
    this.phone,
    this.address,
    this.status,
    this.plan,
    this.expiryDate,
    this.balance,
    this.notes,
    this.password,
    this.additionalData,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (fullName != null) data['full_name'] = fullName;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;
    if (address != null) data['address'] = address;
    if (status != null) data['status'] = status;
    if (plan != null) data['plan'] = plan;
    if (expiryDate != null) data['expiry_date'] = expiryDate!.toIso8601String();
    if (balance != null) data['balance'] = balance;
    if (notes != null) data['notes'] = notes;
    if (password != null) data['password'] = password;
    if (additionalData != null) data['additional_data'] = additionalData;

    return data;
  }
}

// نموذج لاستجابة إضافة/تحديث المستخدم
class UserActionResponse {
  final bool success;
  final String message;
  final UserData? user;
  final Map<String, dynamic>? errors;

  UserActionResponse({
    required this.success,
    required this.message,
    this.user,
    this.errors,
  });

  factory UserActionResponse.fromJson(Map<String, dynamic> json) {
    return UserActionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      user: json['data'] != null ? UserData.fromJson(json['data']) : null,
      errors: json['errors'],
    );
  }
}

// خطط الاشتراك المتاحة
class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final int durationDays;
  final Map<String, dynamic>? features;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    this.features,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      durationDays: json['duration_days'] ?? 30,
      features: json['features'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'duration_days': durationDays,
      'features': features,
    };
  }
}

// الخطط الافتراضية
class DefaultPlans {
  static List<SubscriptionPlan> get plans => [
    SubscriptionPlan(
      id: 'basic',
      name: 'أساسية',
      description: 'خطة أساسية للاستخدام العادي',
      price: 50.0,
      durationDays: 30,
      features: {
        'speed': '10 Mbps',
        'data_limit': '100 GB',
        'support': 'أساسي',
      },
    ),
    SubscriptionPlan(
      id: 'premium',
      name: 'مميزة',
      description: 'خطة مميزة بسرعة أعلى',
      price: 100.0,
      durationDays: 30,
      features: {
        'speed': '50 Mbps',
        'data_limit': '500 GB',
        'support': 'متقدم',
      },
    ),
    SubscriptionPlan(
      id: 'pro',
      name: 'احترافية',
      description: 'خطة احترافية بلا حدود',
      price: 200.0,
      durationDays: 30,
      features: {
        'speed': '100 Mbps',
        'data_limit': 'غير محدود',
        'support': 'مخصص 24/7',
      },
    ),
  ];

  static SubscriptionPlan? getPlanById(String id) {
    try {
      return plans.firstWhere((plan) => plan.id == id);
    } catch (e) {
      return null;
    }
  }
}

// حالات المستخدم المتاحة
enum UserStatus { active, inactive, suspended, expired }

extension UserStatusExtension on UserStatus {
  String get value {
    switch (this) {
      case UserStatus.active:
        return 'active';
      case UserStatus.inactive:
        return 'inactive';
      case UserStatus.suspended:
        return 'suspended';
      case UserStatus.expired:
        return 'expired';
    }
  }

  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'معلق';
      case UserStatus.expired:
        return 'منتهي الصلاحية';
    }
  }

  static UserStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'active':
        return UserStatus.active;
      case 'inactive':
        return UserStatus.inactive;
      case 'suspended':
        return UserStatus.suspended;
      case 'expired':
        return UserStatus.expired;
      default:
        return UserStatus.inactive;
    }
  }

  static List<UserStatus> get allStatuses => UserStatus.values;
}
