import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AllCardsComponent } from './all-cards/all-cards.component';
import { SingleCardComponent } from './single-card/single-card.component';

const routes: Routes = [
  {
    path: '',
    component: AllCardsComponent,
  },
  {
    path: ':id',
    component: SingleCardComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CardsRoutingModule {}
