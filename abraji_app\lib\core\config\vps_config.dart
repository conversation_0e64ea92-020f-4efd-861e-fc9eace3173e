/// إعدادات VPS المحددة
class VpsConfig {
  // معلومات VPS
  static const String vpsIp = '*************';
  static const String vpsUrl = 'http://*************';
  
  // منافذ مختلفة للاختبار
  static const int httpPort = 80;
  static const int httpsPort = 443;
  static const int customPort = 8080;
  static const int laravelPort = 8000;
  
  // مسارات Laravel المحتملة
  static const List<String> laravelPaths = [
    '', // المسار الجذر
    '/public', // إذا كان public folder مكشوف
    '/AbrajiAPIs', // إذا كان في مجلد فرعي
    '/abraji', // اسم مختصر
  ];
  
  // URLs كاملة للاختبار
  static List<String> get testUrls => [
    'http://$vpsIp',
    'http://$vpsIp:$customPort',
    'http://$vpsIp:$laravelPort',
    'https://$vpsIp',
    ...laravelPaths.map((path) => 'http://$vpsIp$path'),
    ...laravelPaths.map((path) => 'http://$vpsIp:$customPort$path'),
  ];
  
  // بيانات اختبار افتراضية
  static const Map<String, String> testCredentials = {
    'admin': 'admin123',
    'test': 'test123',
    'user': 'password',
    'demo': 'demo123',
  };
  
  // Headers خاصة بـ VPS
  static Map<String, String> get vpsHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'AbrajiApp/1.0.0',
    'X-Requested-With': 'XMLHttpRequest',
    'Cache-Control': 'no-cache',
  };
  
  // إعدادات الاتصال
  static const Duration connectionTimeout = Duration(seconds: 15);
  static const Duration readTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  // فحص إذا كان الرابط صالح للـ VPS
  static bool isValidVpsUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    // فحص IP
    if (uri.host == vpsIp) return true;
    
    // فحص إذا كان يحتوي على IP
    if (url.contains(vpsIp)) return true;
    
    return false;
  }
  
  // الحصول على أفضل URL للاختبار
  static String getBestUrl() {
    return vpsUrl;
  }
  
  // إنشاء URL كامل مع endpoint
  static String buildUrl(String baseUrl, String endpoint) {
    final cleanBase = baseUrl.endsWith('/') 
        ? baseUrl.substring(0, baseUrl.length - 1)
        : baseUrl;
    final cleanEndpoint = endpoint.startsWith('/') 
        ? endpoint 
        : '/$endpoint';
    
    return '$cleanBase$cleanEndpoint';
  }
  
  // معلومات الخادم للعرض
  static Map<String, dynamic> get serverInfo => {
    'ip': vpsIp,
    'url': vpsUrl,
    'ports': [httpPort, httpsPort, customPort, laravelPort],
    'paths': laravelPaths,
    'total_test_urls': testUrls.length,
  };
}
