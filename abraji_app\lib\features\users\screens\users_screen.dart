import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../providers/users_provider.dart';
import '../models/user_data_model.dart';
import '../widgets/user_card.dart';
import '../widgets/users_filter_sheet.dart';

class UsersScreen extends StatefulWidget {
  const UsersScreen({super.key});

  @override
  State<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends State<UsersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController.addListener(_onScroll);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeData() async {
    final usersProvider = Provider.of<UsersProvider>(context, listen: false);

    // تعيين معلومات الاتصال بـ SAS Radius من البيانات المحفوظة
    await _setupSasRadiusConnection();

    // تحميل البيانات
    await Future.wait([
      usersProvider.loadUsers(refresh: true),
      usersProvider.loadUserStats(),
      usersProvider.loadOnlineUsers(),
    ]);
  }

  Future<void> _setupSasRadiusConnection() async {
    final usersProvider = Provider.of<UsersProvider>(context, listen: false);

    // يمكن الحصول على URL من SharedPreferences أو من SettingsProvider
    // هنا سنستخدم URL افتراضي للاختبار
    usersProvider.setSasRadiusConnection('http://localhost:8000');
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final usersProvider = Provider.of<UsersProvider>(context, listen: false);
      usersProvider.loadMoreUsers();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'بيانات المستخدمين',
          style: AppTypography.titleLarge.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'النشطين'),
            Tab(text: 'المعلقين'),
            Tab(text: 'المتصلين'),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _showFilterSheet,
            icon: const Icon(Icons.filter_list),
          ),
          Consumer<UsersProvider>(
            builder: (context, usersProvider, child) {
              return IconButton(
                onPressed: usersProvider.isLoading
                    ? null
                    : () {
                        usersProvider.refresh();
                      },
                icon: usersProvider.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
              );
            },
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push('/users/add'),
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة مستخدم'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<UsersProvider>(
        builder: (context, usersProvider, child) {
          if (usersProvider.error != null) {
            return _buildErrorWidget(usersProvider);
          }

          return Column(
            children: [
              // شريط البحث
              _buildSearchBar(usersProvider),

              // قائمة المستخدمين
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildUsersList(usersProvider.users, usersProvider),
                    _buildUsersList(usersProvider.activeUsers, usersProvider),
                    _buildUsersList(
                      usersProvider.suspendedUsers,
                      usersProvider,
                    ),
                    _buildUsersList(usersProvider.onlineUsers, usersProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(UsersProvider usersProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن مستخدم...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    usersProvider.resetFilters();
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.borderLight),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary),
          ),
        ),
        onChanged: (value) {
          setState(() {});
          if (value.isEmpty) {
            usersProvider.resetFilters();
          }
        },
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            usersProvider.searchUsers(value);
          }
        },
      ),
    );
  }

  Widget _buildUsersList(List<UserData> users, UsersProvider usersProvider) {
    if (usersProvider.isLoading && users.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات مستخدمين',
              style: AppTypography.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من الاتصال بـ SAS Radius',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => usersProvider.refresh(),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: users.length + (usersProvider.isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= users.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final user = users[index];
          return UserCard(
            user: user,
            onTap: () => _showUserDetails(user),
            onStatusChanged: (newStatus) {
              usersProvider.updateUserStatus(user.id, newStatus);
            },
            onDisconnect: () {
              usersProvider.disconnectUser(user.id);
            },
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(UsersProvider usersProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text('خطأ في تحميل البيانات', style: AppTypography.titleLarge),
          const SizedBox(height: 8),
          Text(
            usersProvider.error!,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              usersProvider.clearError();
              usersProvider.refresh();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => UsersFilterSheet(
        currentFilters: Provider.of<UsersProvider>(
          context,
          listen: false,
        ).currentFilters,
        onApplyFilters: (filters) {
          Provider.of<UsersProvider>(
            context,
            listen: false,
          ).applyFilters(filters);
        },
      ),
    );
  }

  void _showUserDetails(UserData user) {
    context.push('/users/${user.id}');
  }
}
