<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Carbon\Carbon;

class SasConnection extends Model
{
    use HasFactory;

    protected $fillable = [
        'connection_name',
        'server_url',
        'server_ip',
        'server_port',
        'username',
        'password',
        'shared_secret',
        'connection_type',
        'status',
        'connection_settings',
        'last_tested_at',
        'last_successful_at',
        'last_error',
        'success_count',
        'failure_count',
    ];

    protected $casts = [
        'connection_settings' => 'array',
        'last_tested_at' => 'datetime',
        'last_successful_at' => 'datetime',
        'server_port' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
    ];

    protected $hidden = [
        'password',
        'shared_secret',
    ];

    /**
     * تشفير كلمة المرور عند الحفظ
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Crypt::encryptString($value);
    }

    /**
     * فك تشفير كلمة المرور عند القراءة
     */
    public function getPasswordAttribute($value)
    {
        try {
            return Crypt::decryptString($value);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * تشفير المفتاح المشترك عند الحفظ
     */
    public function setSharedSecretAttribute($value)
    {
        $this->attributes['shared_secret'] = Crypt::encryptString($value);
    }

    /**
     * فك تشفير المفتاح المشترك عند القراءة
     */
    public function getSharedSecretAttribute($value)
    {
        try {
            return Crypt::decryptString($value);
        } catch (\Exception $e) {
            return 'testing123'; // قيمة افتراضية
        }
    }

    /**
     * استخراج IP من URL
     */
    public function setServerUrlAttribute($value)
    {
        $this->attributes['server_url'] = $value;
        
        // استخراج IP من URL
        $parsed = parse_url($value);
        if (isset($parsed['host'])) {
            $this->attributes['server_ip'] = $parsed['host'];
        }
        
        // استخراج المنفذ
        if (isset($parsed['port'])) {
            $this->attributes['server_port'] = $parsed['port'];
        } elseif (isset($parsed['scheme'])) {
            $this->attributes['server_port'] = $parsed['scheme'] === 'https' ? 443 : 80;
        }
        
        // تحديد نوع الاتصال
        if (isset($parsed['scheme'])) {
            $this->attributes['connection_type'] = $parsed['scheme'];
        }
    }

    /**
     * تسجيل نجاح الاتصال
     */
    public function recordSuccess()
    {
        $this->update([
            'last_tested_at' => now(),
            'last_successful_at' => now(),
            'success_count' => $this->success_count + 1,
            'status' => 'active',
            'last_error' => null,
        ]);
    }

    /**
     * تسجيل فشل الاتصال
     */
    public function recordFailure($error = null)
    {
        $this->update([
            'last_tested_at' => now(),
            'failure_count' => $this->failure_count + 1,
            'status' => 'inactive',
            'last_error' => $error,
        ]);
    }

    /**
     * فحص إذا كان الاتصال نشط
     */
    public function isActive()
    {
        return $this->status === 'active' && 
               $this->last_successful_at && 
               $this->last_successful_at->diffInHours(now()) < 24;
    }

    /**
     * الحصول على معدل النجاح
     */
    public function getSuccessRateAttribute()
    {
        $total = $this->success_count + $this->failure_count;
        return $total > 0 ? round(($this->success_count / $total) * 100, 2) : 0;
    }

    /**
     * الحصول على آخر حالة اختبار
     */
    public function getLastTestStatusAttribute()
    {
        if (!$this->last_tested_at) {
            return 'لم يتم الاختبار';
        }

        if ($this->last_successful_at && 
            $this->last_successful_at->equalTo($this->last_tested_at)) {
            return 'نجح';
        }

        return 'فشل';
    }

    /**
     * الحصول على الاتصال النشط
     */
    public static function getActiveConnection()
    {
        return static::where('status', 'active')
                    ->orderBy('last_successful_at', 'desc')
                    ->first();
    }

    /**
     * إنشاء أو تحديث اتصال
     */
    public static function createOrUpdate($data)
    {
        // البحث عن اتصال موجود بنفس URL
        $existing = static::where('server_url', $data['server_url'])
                         ->where('username', $data['username'])
                         ->first();

        if ($existing) {
            $existing->update($data);
            return $existing;
        }

        return static::create($data);
    }

    /**
     * تنظيف الاتصالات القديمة
     */
    public static function cleanupOldConnections()
    {
        // حذف الاتصالات غير النشطة لأكثر من 30 يوم
        static::where('status', 'inactive')
              ->where('updated_at', '<', now()->subDays(30))
              ->delete();

        // تحديث حالة الاتصالات القديمة
        static::where('status', 'active')
              ->where('last_successful_at', '<', now()->subDays(7))
              ->update(['status' => 'inactive']);
    }
}
