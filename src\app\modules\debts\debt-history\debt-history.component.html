<section class=" p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
  <div *ngIf="isLoading">
    <app-table-skeleton></app-table-skeleton>
  </div>
  <div *ngIf="!isLoading" class="shadow-sm">
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
      <div class="flex items-center m-4">
        <i class="pi pi-history me-2" style="font-size: 1rem"></i>
        <label for="">
          <span class="font-bold">{{ 'debts.history_of_payments' | transloco }}</span></label>
      </div>
      <!-- data simple table -->
      <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" class="px-6 py-3"> # </th>
            <th scope="col" class="px-6 py-3"> {{'debts.amount' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'debts.paid_at' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'debts.created_at' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'debts.updated_at' | transloco}} </th>
            <th scope="col" class="px-6 py-3"> {{'table.actions' | transloco}} </th>
          </tr>
        </thead>
        <tbody>
          <tr class="bg-white border-b dark:bg-gray-900 dark:border-gray-700"
            *ngFor="let row of debtHistory; let i = index">
            <!-- add counter column  -->
            <td class="px-6 py-4">{{ i + 1 }}</td>
            <td class="px-6 py-4 font-bold">{{ row.amount }}</td>
            <td class="px-6 py-4">{{ row.paid_at | date:'medium' }}</td>
            <td class="px-6 py-4">{{ row.created_at | date:'medium' }}</td>
            <td class="px-6 py-4">{{ row.updated_at | date:'medium' }}</td>
            <td class="px-6 py-4">edit</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>
