<section appFlowbiteInit class="bg-gray-50 dark:bg-gray-900 p-3">
  <div class="overflow-x-auto mt-8">
    <!-- series details -->
    <div *ngIf="seriesIsLoading">
      <app-small-card-skeleton></app-small-card-skeleton>
    </div>
    <div *ngIf="!seriesIsLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.series" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.series}}</p>
      </div>
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.expiration" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.expiration}}</p>
      </div>
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.value" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.value}}</p>
      </div>
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.owner" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.owner}}</p>
      </div>
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.createdBy" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.created_by}}</p>
      </div>
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <h3 class="text-gray-500">{{ "card.series_date" | transloco }}</h3>
        <p class="font-bold">{{seriesDetails.created_at}}</p>
      </div>
    </div>
    <!-- all cards in series -->
    <div *ngIf="tableIsLoading">
      <app-table-skeleton></app-table-skeleton>
    </div>
    <section *ngIf="!tableIsLoading" class="bg-gray-50 dark:bg-gray-900 my-4">
      <div appFlowbiteInit class="mx-auto ">
        <!-- Start coding here -->
        <div appFlowbiteInit
          class="bg-white dark:bg-gray-800 relative shadow-lg border-2 sm:rounded-lg overflow-auto">
          <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
            <div class="w-full md:w-1/2">
              <form class="flex items-center">
                <label for="simple-search" class="sr-only">{{'table.search' | transloco}}</label>
                <div class="relative w-full">
                  <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                      viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <input type="text" id="simple-search"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                    placeholder="{{'table.search' | transloco}}..." required="" [value]="requestForm.search"
                    (change)="searchChange($event)">
                </div>
              </form>
            </div>
            <div
              class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
              <div class="flex items-center space-x-3 w-full md:w-auto">
                <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown"
                  class="w-full md:w-auto flex items-center justify-center me-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  {{'table.actions' | transloco}}

                  <svg class="-ml-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>

                <div id="actionsDropdown"
                  class="hidden z-30 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                  <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                    <li *ngIf="!(exportIsLoading || tableResponse.total === 0)">
                      <a (click)="exportToExcel()"
                        class="cursor-pointer flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                        <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 10V4a1 1 0 0 0-1-1H9.914a1 1 0 0 0-.707.293L5.293 7.207A1 1 0 0 0 5 7.914V20a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2M10 3v4a1 1 0 0 1-1 1H5m5 6h9m0 0-2-2m2 2-2 2" />
                        </svg>
                        Export all to excel
                      </a>
                    </li>

                  </ul>
                </div>
                <button id="ColumnsDropdownButton" data-dropdown-toggle="ColumnsDropdown"
                  class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  <svg class="h-4 w-4 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                  </svg>

                  {{'table.columns' | transloco}}
                  <svg class="-mr-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
                <div id="ColumnsDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                  <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.showColumns' | transloco}}
                  </h6>
                  <ul class="space-y-2 text-sm" aria-labelledby="ColumnsDropdownButton">
                    <li *ngFor="let column of cardDetailsColumnsState" class="flex items-center">
                      <input title="{{column.key}}" type="checkbox" [id]="column.key" [checked]="!column.hidden"
                        (change)="toggleColumnSelection(column.key)"
                        class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                      <label [for]="column.key" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ ('card.cardDetails.' + column.key) | transloco | titlecase }}
                      </label>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <!-- data table -->
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
              <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                  <th scope="col" class="px-2 py-3">#</th>
                  <ng-container *ngFor="let column of cardDetailsColumnsState">
                    <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                      <!-- provide sorting column -->
                      <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                        {{ ('card.cardDetails.' + column.key) | transloco | titlecase }}
                        <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                          class="w-3 h-3 ms-1.5">
                          <path _ngcontent-ng-c3067077598=""
                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                          </path>
                        </svg>
                      </div>
                    </th>
                  </ng-container>
                  <th scope="col" class="px-4 py-3">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let card of tableResponse.data; let i = index" class="border-b dark:border-gray-700">
                  <td class="px-4 py-3">{{ tableResponse.from + i }}</td>
                  <ng-container *ngFor="let column of cardDetailsColumnsState">
                    <td class="px-4 py-3" *ngIf="!column.hidden">
                      <ng-container *ngIf="column.key === 'series'; else normalColumn">
                        <a [routerLink]="getPropertyValue(card, column.key)" class="text-blue-600">
                          {{ getPropertyValue(card, column.key) }}
                        </a>
                      </ng-container>
                      <ng-template #normalColumn>
                        <ng-container>
                          {{ getPropertyValue(card, column.key) }}
                        </ng-container>
                      </ng-template>
                    </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
          </div>
          <nav class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
            aria-label="Table navigation">
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
              {{'table.showing' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null?
                tableResponse.from
                :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
              {{'table.of' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
            </span>
            <!-- Pagination controls -->
            <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
              <li>
                <button (click)="changePage((tableResponse.current_page - 1).toString())"
                  [disabled]="tableResponse.current_page === 1"
                  class="flex items-center justify-center h-full py-1.5 px-3 ms-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Previous</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </li>
              <ng-container *ngFor="let page of getPagesToDisplay()">
                <li *ngIf="page === '...'">
                  <span
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
                </li>
                <li *ngIf="page !== '...'">
                  <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
                    [class.text-primary-600]="tableResponse.current_page === page"
                    [class.z-10]="tableResponse.current_page === page"
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    {{ page }}
                  </button>
                </li>
              </ng-container>
              <li>
                <button (click)="changePage((tableResponse.current_page + 1).toString())"
                  [disabled]="tableResponse.current_page === tableResponse.last_page"
                  class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Next</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                      clip-rule="evenodd" />
                  </svg>

                </button>
              </li>
            </ul>


          </nav>
        </div>
      </div>
    </section>
  </div>
</section>
