import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { validateIdNumberGuard } from '../../core/guards/validate-id-number.guard';
import { ExpensesComponent } from './expenses/expenses.component';

const routes: Routes = [
  {
    path: 'expenses',
    component: ExpensesComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ResourcesRoutingModule { }
