import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { initFlowbite } from 'flowbite';
import { SharedModule } from './modules/shared/shared.module';

@Component({
    selector: 'app-root',
    standalone: true,
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss',
    imports: [RouterOutlet, SharedModule]
})
export class AppComponent implements OnInit {
  title = 'Abraji-Web';

  ngOnInit(): void {
    initFlowbite();
  }
}
