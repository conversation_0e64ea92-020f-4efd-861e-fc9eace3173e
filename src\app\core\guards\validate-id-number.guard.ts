import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

export const validateIdNumberGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const id = route.paramMap.get('id');

  // Check if the id is a number
  if (id && !isNaN(+id)) {
    return true;
  }

  // If id is not a number, navigate to home
  router.navigate(['/']);
  return false;
};
