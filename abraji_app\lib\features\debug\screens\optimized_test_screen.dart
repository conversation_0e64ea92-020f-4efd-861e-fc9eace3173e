import 'package:flutter/material.dart';
import '../../../core/services/sas_api_service.dart';
import '../../../core/config/vps_config.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';

/// صفحة اختبار محسنة بناءً على نتائج VPS
class OptimizedTestScreen extends StatefulWidget {
  const OptimizedTestScreen({super.key});

  @override
  State<OptimizedTestScreen> createState() => _OptimizedTestScreenState();
}

class _OptimizedTestScreenState extends State<OptimizedTestScreen> {
  final _urlController = TextEditingController(text: VpsConfig.vpsUrl);
  final _usernameController = TextEditingController(text: 'admin');
  final _passwordController = TextEditingController(text: 'admin123');

  bool _isLoading = false;
  String _result = '';
  SasApiService? _apiService;

  @override
  void dispose() {
    _urlController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// دالة مساعدة لتحديث الحالة بأمان
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  /// اختبار محسن للمصادقة مع حفظ البيانات
  Future<void> _testOptimizedAuth() async {
    if (!mounted) return;

    _safeSetState(() {
      _isLoading = true;
      _result =
          '🚀 اختبار محسن للمصادقة مع حفظ البيانات...\n\n'
          '1️⃣ حفظ بيانات SAS في قاعدة البيانات...\n'
          '2️⃣ اختبار المصادقة...\n'
          '📍 استخدام: ${_urlController.text}/api/sas/authenticate\n'
          '👤 المستخدم: ${_usernameController.text}\n\n';
    });

    try {
      _apiService = SasApiService(baseUrl: _urlController.text);
      final response = await _apiService!.authenticate(
        username: _usernameController.text,
        password: _passwordController.text,
      );

      _safeSetState(() {
        if (response.success) {
          _result +=
              '✅ نجحت المصادقة!\n\n'
              '🎯 النتائج:\n'
              '• الاسم: ${response.user?.name ?? "غير متوفر"}\n'
              '• اسم المستخدم: ${response.user?.username ?? "غير متوفر"}\n'
              '• البريد: ${response.user?.email ?? "غير متوفر"}\n'
              '• الدور: ${response.user?.role ?? "غير متوفر"}\n'
              '• الحالة: ${response.user?.status ?? "غير متوفر"}\n'
              '• Token: ${response.token != null ? "✅ متوفر" : "❌ غير متوفر"}\n\n'
              '🎉 يمكنك الآن استخدام التطبيق للاتصال مع VPS!';
        } else {
          _result +=
              '❌ فشلت المصادقة\n\n'
              '📋 التفاصيل:\n'
              '• السبب: ${response.message}\n\n'
              '🔧 الحلول المقترحة:\n'
              '• تحقق من اسم المستخدم وكلمة المرور\n'
              '• تأكد من أن Laravel يعمل على VPS\n'
              '• تحقق من إعدادات قاعدة البيانات';
        }
      });
    } catch (e) {
      _safeSetState(() {
        _result +=
            '❌ خطأ في الاتصال\n\n'
            '📋 التفاصيل:\n'
            '• الخطأ: $e\n\n'
            '🔧 الحلول المقترحة:\n'
            '• تحقق من اتصال الإنترنت\n'
            '• تأكد من أن VPS يعمل\n'
            '• جرب IP مختلف أو منفذ مختلف';
      });
    } finally {
      _safeSetState(() {
        _isLoading = false;
      });
    }
  }

  /// اختبار محسن للمستخدمين
  Future<void> _testOptimizedUsers() async {
    setState(() {
      _isLoading = true;
      _result =
          '🚀 اختبار محسن للمستخدمين...\n\n'
          '📍 جاري اختبار endpoints متعددة...\n';
    });

    try {
      _apiService ??= SasApiService(baseUrl: _urlController.text);

      // اختبار /api/users/table أولاً (الأكثر احتمالاً للعمل)
      setState(() {
        _result += '🔍 اختبار /api/users/table...\n';
      });

      final response = await _apiService!.getUsers(page: 1, limit: 5);

      setState(() {
        if (response.success && response.users.isNotEmpty) {
          _result +=
              '✅ نجح جلب المستخدمين!\n\n'
              '📊 النتائج:\n'
              '• عدد المستخدمين: ${response.users.length}\n'
              '• الصفحة: ${response.pagination?.currentPage ?? 1}\n'
              '• إجمالي الصفحات: ${response.pagination?.totalPages ?? 1}\n\n'
              '👥 قائمة المستخدمين:\n';

          for (int i = 0; i < response.users.length && i < 5; i++) {
            final user = response.users[i];
            _result +=
                '${i + 1}. ${user.name} (${user.username}) - ${user.status}\n';
          }

          _result += '\n🎉 يمكنك الآن عرض المستخدمين في التطبيق!';
        } else {
          _result +=
              '⚠️ تم الاتصال لكن لا توجد بيانات\n\n'
              '📋 التفاصيل:\n'
              '• الرسالة: ${response.message ?? "لا توجد بيانات"}\n\n'
              '🔧 الحلول المقترحة:\n'
              '• تحقق من وجود مستخدمين في قاعدة البيانات\n'
              '• تأكد من صحة تنسيق البيانات\n'
              '• جرب إضافة مستخدمين تجريبيين';
        }
      });
    } catch (e) {
      setState(() {
        _result +=
            '❌ خطأ في جلب المستخدمين\n\n'
            '📋 التفاصيل:\n'
            '• الخطأ: $e\n\n'
            '💡 ملاحظة:\n'
            'HTTP 422 يعني أن الخادم يعمل لكن يحتاج تنسيق مختلف للبيانات\n'
            'هذا طبيعي ويمكن إصلاحه في Laravel API';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// اختبار سريع شامل
  Future<void> _quickTest() async {
    setState(() {
      _isLoading = true;
      _result = '⚡ اختبار سريع شامل...\n\n';
    });

    // اختبار المصادقة
    setState(() {
      _result += '1️⃣ اختبار المصادقة...\n';
    });

    await _testOptimizedAuth();

    await Future.delayed(const Duration(seconds: 1));

    // اختبار المستخدمين
    setState(() {
      _result += '\n2️⃣ اختبار المستخدمين...\n';
    });

    await _testOptimizedUsers();

    setState(() {
      _result += '\n🏁 انتهى الاختبار الشامل!';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار محسن للـ VPS'),
        backgroundColor: AppColors.success,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Info Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.success.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: AppColors.success,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'اختبار محسن بناءً على نتائج VPS',
                          style: AppTypography.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'هذا الاختبار محسن خصيص<|im_start|> لـ VPS الخاص بك بناءً على النتائج السابقة:\n'
                      '• ✅ /api/auth/login يعمل\n'
                      '• ⚠️ /api/users/* يحتاج تحسين\n'
                      '• 🌐 الاتصال الأساسي ممتاز',
                      style: AppTypography.bodyMedium.copyWith(
                        color: AppColors.success,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // URL Input
              TextField(
                controller: _urlController,
                decoration: const InputDecoration(
                  labelText: 'رابط VPS',
                  hintText: 'http://161.97.130.54',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.dns),
                ),
                readOnly: true,
              ),

              const SizedBox(height: 16),

              // Credentials
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المستخدم',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.lock),
                      ),
                      obscureText: true,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Test Buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testOptimizedAuth,
                      icon: const Icon(Icons.login, size: 20),
                      label: const Text('اختبار المصادقة المحسن'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testOptimizedUsers,
                      icon: const Icon(Icons.people, size: 20),
                      label: const Text('اختبار المستخدمين المحسن'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.info,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _quickTest,
                      icon: const Icon(Icons.flash_on, size: 20),
                      label: const Text('اختبار سريع شامل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Results
              Container(
                constraints: BoxConstraints(
                  minHeight: 200,
                  maxHeight: MediaQuery.of(context).size.height * 0.5,
                ),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: _isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('جاري التحميل...'),
                          ],
                        ),
                      )
                    : SingleChildScrollView(
                        child: SelectableText(
                          _result.isEmpty
                              ? '🎯 النتائج ستظهر هنا...\n\n'
                                    'جرب الاختبار السريع الشامل للحصول على تقرير كامل!'
                              : _result,
                          style: AppTypography.bodyMedium.copyWith(
                            fontFamily: 'monospace',
                            height: 1.5,
                          ),
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
