import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  Locale _currentLocale = const Locale('ar', 'SA'); // Default to Arabic
  String _currentFontFamily = 'Cairo'; // Default Arabic font

  Locale get currentLocale => _currentLocale;
  String get currentFontFamily => _currentFontFamily;
  bool get isArabic => _currentLocale.languageCode == 'ar';
  bool get isEnglish => _currentLocale.languageCode == 'en';

  // Font families for different languages
  static const String arabicFont = 'Cairo';
  static const String englishFont = 'Roboto';

  LanguageProvider() {
    _loadLanguage();
  }

  Future<void> _loadLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString('language_code') ?? 'ar';
      final countryCode = prefs.getString('country_code') ?? 'SA';
      
      _currentLocale = Locale(languageCode, countryCode);
      _updateFontFamily();
      notifyListeners();
    } catch (e) {
      // Handle error, keep default values
      debugPrint('Error loading language: $e');
    }
  }

  Future<void> changeLanguage(Locale locale) async {
    if (_currentLocale == locale) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language_code', locale.languageCode);
      await prefs.setString('country_code', locale.countryCode ?? '');

      _currentLocale = locale;
      _updateFontFamily();
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving language: $e');
    }
  }

  void _updateFontFamily() {
    switch (_currentLocale.languageCode) {
      case 'ar':
        _currentFontFamily = arabicFont;
        break;
      case 'en':
        _currentFontFamily = englishFont;
        break;
      default:
        _currentFontFamily = arabicFont;
    }
  }

  // Helper methods for easy language switching
  Future<void> setArabic() async {
    await changeLanguage(const Locale('ar', 'SA'));
  }

  Future<void> setEnglish() async {
    await changeLanguage(const Locale('en', 'US'));
  }

  // Get text direction based on current language
  TextDirection get textDirection {
    return _currentLocale.languageCode == 'ar' 
        ? TextDirection.rtl 
        : TextDirection.ltr;
  }

  // Get appropriate text style with correct font
  TextStyle getTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamily: _currentFontFamily,
    );
  }
}
