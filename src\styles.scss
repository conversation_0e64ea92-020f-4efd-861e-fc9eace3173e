@import "tailwindcss/base";

@import "tailwindcss/components";

// add fonts
@font-face {
  font-family: "PNU-Bold";
  src: url("../public/assets/fonts/PNU-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "PNU-Regular";
  src: url("../public/assets/fonts/PNU-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "PNU-Light";
  src: url("../public/assets/fonts/PNU-Light.ttf") format("truetype");
  font-weight: lighter;
  font-style: normal;
}

.active-link {
  &:focus {
    color: #7c3aed; /* focus:text-violet-700 */
    background-color: #f3f4f6; /* focus:bg-gray-100 */
    border-spacing: 2px; /* focus:border-spacing-2 */
    border-color: #7c3aed; /* focus:border-violet-700 */
  }
  transition: all 0.75s; /* transition duration-75 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  //font-family: "Roboto", sans-serif;
  font-family: "PNU-Regular", sans-serif !important;
  background-color: #fff;
}
@import "primeicons/primeicons.css";

@import "tailwindcss/utilities";
