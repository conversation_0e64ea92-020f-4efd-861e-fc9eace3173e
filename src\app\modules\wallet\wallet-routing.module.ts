import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { WalletHomeComponent } from './wallet-home/wallet-home.component';
import { WalletTransactionsComponent } from './wallet-transactions/wallet-transactions.component';

const routes: Routes = [
  {
    path: '',
    component: WalletHomeComponent,
  },
  {
    path: 'transactions',
    component: WalletTransactionsComponent,
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class WalletRoutingModule { }
