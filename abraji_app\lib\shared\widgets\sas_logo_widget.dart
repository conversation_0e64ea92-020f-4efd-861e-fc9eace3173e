import 'package:flutter/material.dart';
import 'sas_magic_icon.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';

class SasLogoWidget extends StatelessWidget {
  final double size;
  final bool isConnected;
  final bool showText;
  final String? customText;
  final bool enableAnimation;
  final VoidCallback? onTap;

  const SasLogoWidget({
    super.key,
    this.size = 80,
    this.isConnected = false,
    this.showText = true,
    this.customText,
    this.enableAnimation = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final widget = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // الأيقونة السحرية
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                isConnected 
                    ? AppColors.success.withValues(alpha: 0.2)
                    : AppColors.primary.withValues(alpha: 0.2),
                isConnected 
                    ? AppColors.success.withValues(alpha: 0.05)
                    : AppColors.primary.withValues(alpha: 0.05),
                Colors.transparent,
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: (isConnected ? AppColors.success : AppColors.primary)
                    .withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Center(
            child: SasMagicIcon(
              size: size * 0.6,
              isConnected: isConnected,
              primaryColor: isConnected ? AppColors.success : AppColors.primary,
              secondaryColor: (isConnected ? AppColors.success : AppColors.primary)
                  .withValues(alpha: 0.3),
              enableAnimation: enableAnimation,
            ),
          ),
        ),

        if (showText) ...[
          const SizedBox(height: 12),
          
          // النص
          Text(
            customText ?? 'SAS Radius',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: isConnected ? AppColors.success : AppColors.primary,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // حالة الاتصال
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: (isConnected ? AppColors.success : AppColors.error)
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: (isConnected ? AppColors.success : AppColors.error)
                    .withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: isConnected ? AppColors.success : AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  isConnected ? 'متصل' : 'غير متصل',
                  style: AppTypography.labelSmall.copyWith(
                    color: isConnected ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(size / 2),
        child: widget,
      );
    }

    return widget;
  }
}

// ويدجت مبسط للاستخدام في الأماكن الصغيرة
class SasIconBadge extends StatelessWidget {
  final double size;
  final bool isConnected;
  final bool enableAnimation;

  const SasIconBadge({
    super.key,
    this.size = 24,
    this.isConnected = false,
    this.enableAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: (isConnected ? AppColors.success : AppColors.primary)
            .withValues(alpha: 0.1),
        border: Border.all(
          color: (isConnected ? AppColors.success : AppColors.primary)
              .withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: SasMagicIcon(
          size: size * 0.7,
          isConnected: isConnected,
          primaryColor: isConnected ? AppColors.success : AppColors.primary,
          enableAnimation: enableAnimation,
        ),
      ),
    );
  }
}

// ويدجت للاستخدام في الهيدر
class SasHeaderIcon extends StatelessWidget {
  final bool isConnected;
  final VoidCallback? onTap;

  const SasHeaderIcon({
    super.key,
    this.isConnected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [
              Colors.white.withValues(alpha: 0.3),
              Colors.white.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Center(
              child: SasMagicIcon(
                size: 24,
                isConnected: isConnected,
                primaryColor: Colors.white,
                secondaryColor: Colors.white.withValues(alpha: 0.3),
                enableAnimation: true,
              ),
            ),
            
            // مؤشر الاتصال
            if (isConnected)
              Positioned(
                right: 2,
                top: 2,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppColors.success,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
