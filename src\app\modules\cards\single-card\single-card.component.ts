import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeriesService } from '../../../core/cards-services/services/series.service';
import { ToastService } from '../../shared/toast/toast.service';
import { CardDetails, initialCardDetailsColumnsState, SeriesDetails, SeriesDetailsForm } from '../../../core/cards-services/api/series';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { ColumnState, TableResponse } from '../../../core/common-services/interfaces/table-response';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { ExcelService } from '../../../core/common-services/services/excel.service';


@Component({
  selector: 'app-single-card',
  templateUrl: './single-card.component.html',
  styleUrl: './single-card.component.scss'
})
export class SingleCardComponent implements OnInit, AfterViewInit {
  private cardSeries!: string | null;
  seriesDetails!: SeriesDetails;
  seriesIsLoading = false;
  tableIsLoading = false;
  exportIsLoading = false;
  requestForm!: SeriesDetailsForm;
  cardDetailsColumnsState!: ColumnState[];
  tableResponse: TableResponse<CardDetails> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };


  constructor(
    private route: ActivatedRoute,
    private seriesService: SeriesService,
    private toastService: ToastService,
    private localStorageService: LocalStorageService,
    private tableElementsService: TableElementsService,
    private excelService: ExcelService,
  ) { }

  ngOnInit(): void {
    // initiate columns state
    this.cardDetailsColumnsState = this.localStorageService
    .loadColumnsState(this.localStorageService.CardDetailsColumnsState)
    || initialCardDetailsColumnsState;

    this.requestForm = {
      page: 1,
      count: 10,
      sortBy: "",
      direction: "",
      search: "",
      columns: this.getVisibleColumns(),
    };

    // Access route to get the id parameter
    this.route?.paramMap.subscribe((params) => {
      this.cardSeries = params.get('id');

      if (this.cardSeries) {
        // fetch the card details
        this.getCardDetails(this.cardSeries);
        this.loadSeries();
      }
    });
  }

  ngAfterViewInit(): void {
  }


  getCardDetails(id: string): void {
    // fetch the data
    this.seriesIsLoading = true;
    this.seriesService.getCardById(id).subscribe({
      next: (response: any) => {
        console.log('before', response);

        this.seriesDetails = response.data;
        console.log('my series ', this.seriesDetails);
        this.seriesIsLoading = false;
      },
      error: (error) => {
        this.seriesIsLoading = false;
        this.toastService.addToast('error', 'Error', 'Error fetching card details');
      }
    });
  }

  // tables methods
  searchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.requestForm.search = inputElement.value;
    this.requestForm.page = 1;
    this.loadSeries();
  }

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.requestForm);
    // fetch data
    this.loadSeries();
  }

  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(this.tableResponse.last_page, this.requestForm.page);
  }

  // Bind the change page in pagination to the form
  changePage(page: (string | number)): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.requestForm.page = pageNumber;
      this.loadSeries();
    }
  }

  // Bind the change in columns visibility
  toggleColumnSelection(columnKey: string) {
    this.tableElementsService.toggleColumnSelection(columnKey, this.cardDetailsColumnsState, this.localStorageService.CardDetailsColumnsState);
  }

  loadSeries(): void {
    this.tableIsLoading = true;
    this.seriesService.getListCardForSeries(String(this.cardSeries), this.requestForm).subscribe({
      next: (response: any) => {
        console.log(response);
        this.tableResponse = response;
        this.tableIsLoading = false;
    },
    error: (error: any) => {
      this.tableIsLoading = false;
      this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the table data');
      console.error('There was an error!', error);
    },
    });
  }

  getPropertyValue(cardDetails: CardDetails, key: string): any {
    switch (key) {
      case 'used_by_manager':
        return cardDetails?.manager_details?.username;
      case 'used_by_user':
        return cardDetails?.user_details?.username;
      default:
        return cardDetails[key as keyof CardDetails];
    }
  }

  exportToExcel(): void {
    console.log('exporting to excel...');

    this.exportIsLoading = true;
    // create request form to get all data
    const myRequestForm = {...this.requestForm};
    myRequestForm.count = this.tableResponse.total;
    myRequestForm.page = 1;
    // fetch all data
    this.seriesService.getListCardForSeries(String(this.cardSeries), myRequestForm).subscribe({
      next: (response: any) => {
        this.exportIsLoading = false;
        try {
          // map the data to the format that will be exported
          const data = this.seriesService.mapCardDetailsToExportFormat(response.data);
          this.excelService.exportAsExcelFile(data, `card_details_${this.cardSeries}`);
        } catch (error) {
          this.toastService.addToast('error', 'Error Message', 'There was an error on exporting the data to excel sheet');
          console.error('There was an error!', error);
        }
      },
      error: (error: any) => {
        this.exportIsLoading = false;
        this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the table data');
        console.error('There was an error!', error);
      },
    });

  }

  private getVisibleColumns(): string[] {
    return this.tableElementsService.getVisibleColumns(this.cardDetailsColumnsState);
  }
}
