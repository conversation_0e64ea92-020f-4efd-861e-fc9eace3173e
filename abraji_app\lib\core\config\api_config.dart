class ApiConfig {
  // إعدادات الخادم
  static const String productionBaseUrl = 'http://161.97.130.54'; // VPS الحقيقي
  static const String stagingBaseUrl = 'http://161.97.130.54:8080'; // للاختبار
  static const String developmentBaseUrl = 'http://161.97.130.54';

  // البيئة الحالية
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static const bool isStaging = bool.fromEnvironment('STAGING');

  // الحصول على الـ base URL حسب البيئة
  static String get baseUrl {
    if (isProduction) {
      return productionBaseUrl;
    } else if (isStaging) {
      return stagingBaseUrl;
    } else {
      return developmentBaseUrl;
    }
  }

  // إعدادات API
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const int defaultPageSize = 20;
  static const int maxRetries = 3;

  // Headers افتراضية
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'AbrajiApp/1.0.0',
  };

  // Endpoints (based on actual AbrajiAPIs structure)
  static const String authEndpoint = '/api/auth/login';
  static const String usersTableEndpoint = '/api/users/table';
  static const String usersOnlineEndpoint = '/api/users/online';
  static const String createUserEndpoint = '/api/users/create';
  static const String seedAllEndpoint = '/api/seed-all';

  // Authentication endpoints (in order of preference)
  static const List<String> authEndpoints = [
    '/api/auth/login', // Primary endpoint from Authentication module
    '/admin/api/index.php/api/login', // SAS Radius format from LoginController
    '/api/login', // Fallback
    '/login', // Simple fallback
  ];

  // Users endpoints (in order of preference)
  static const List<String> usersEndpoints = [
    '/api/users/table', // Primary endpoint from Users module
    '/api/users/online', // Online users
    '/api/users', // Simple users
    '/api/subscribers', // Alternative naming
    '/users', // Simple fallback
  ];

  // إعدادات الأمان
  static const bool enableSSLPinning = true; // في الإنتاج
  static const List<String> allowedCertificates = [
    // إضافة SHA-256 fingerprints للشهادات المسموحة
  ];
}
