import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../models/debt_models.dart';
import '../widgets/debt_card.dart';
import '../widgets/debts_summary_card.dart';
import '../widgets/debts_filter_sheet.dart';

class DebtsScreen extends StatefulWidget {
  const DebtsScreen({super.key});

  @override
  State<DebtsScreen> createState() => _DebtsScreenState();
}

class _DebtsScreenState extends State<DebtsScreen> {
  List<Debt> debts = [];
  DebtsSummary? summary;
  bool isLoading = true;
  DebtFilters currentFilters = DebtFilters();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadDebts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // تحميل المزيد من البيانات
      _loadMoreDebts();
    }
  }

  Future<void> _loadDebts() async {
    setState(() {
      isLoading = true;
    });

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      debts = _generateMockDebts();
      summary = _generateMockSummary();
      isLoading = false;
    });
  }

  Future<void> _loadMoreDebts() async {
    // محاكاة تحميل المزيد من البيانات
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      debts.addAll(_generateMockDebts());
    });
  }

  List<Debt> _generateMockDebts() {
    return [
      Debt(
        id: 'debt_1',
        userId: 'user_1',
        userName: 'أحمد محمد',
        amount: 500.0,
        description: 'رسوم اشتراك شهري',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        dueDate: DateTime.now().add(const Duration(days: 10)),
        status: 'pending',
        type: 'service',
        reference: 'SRV001',
      ),
      Debt(
        id: 'debt_2',
        userId: 'user_2',
        userName: 'فاطمة علي',
        amount: 200.0,
        description: 'غرامة تأخير',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        dueDate: DateTime.now().subtract(const Duration(days: 5)),
        status: 'overdue',
        type: 'penalty',
        reference: 'PEN001',
      ),
      Debt(
        id: 'debt_3',
        userId: 'user_3',
        userName: 'محمد سالم',
        amount: 300.0,
        description: 'رسوم إضافية',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        dueDate: DateTime.now().subtract(const Duration(days: 10)),
        status: 'paid',
        type: 'service',
        reference: 'SRV002',
        payments: [
          DebtPayment(
            id: 'pay_1',
            debtId: 'debt_3',
            amount: 300.0,
            createdAt: DateTime.now().subtract(const Duration(days: 8)),
            status: 'completed',
            method: 'cash',
            reference: 'CASH001',
          ),
        ],
      ),
    ];
  }

  DebtsSummary _generateMockSummary() {
    return DebtsSummary(
      totalAmount: 1000.0,
      paidAmount: 300.0,
      pendingAmount: 500.0,
      overdueAmount: 200.0,
      totalCount: 3,
      paidCount: 1,
      pendingCount: 1,
      overdueCount: 1,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إدارة الديون'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
            tooltip: 'فلترة',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddDebtDialog,
            tooltip: 'إضافة دين جديد',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDebts,
              child: Column(
                children: [
                  // ملخص الديون
                  if (summary != null)
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: DebtsSummaryCard(summary: summary!),
                    ),

                  // قائمة الديون
                  Expanded(
                    child: debts.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: debts.length,
                            itemBuilder: (context, index) {
                              final debt = debts[index];
                              return DebtCard(
                                debt: debt,
                                onTap: () => _showDebtDetails(debt),
                                onPayment: () => _showPaymentDialog(debt),
                                onEdit: () => _showEditDebtDialog(debt),
                                onDelete: () => _showDeleteConfirmation(debt),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد ديون',
            style: AppTypography.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم تسجيل أي ديون حتى الآن',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddDebtDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة دين جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DebtsFilterSheet(
        currentFilters: currentFilters,
        onApplyFilters: (filters) {
          setState(() {
            currentFilters = filters;
          });
          _loadDebts();
        },
      ),
    );
  }

  void _showAddDebtDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح نافذة إضافة دين جديد')),
    );
  }

  void _showDebtDetails(Debt debt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الدين ${debt.id}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('المستخدم: ${debt.userName}'),
              Text('المبلغ: ${debt.amount.toStringAsFixed(2)} ر.س'),
              Text('الوصف: ${debt.description}'),
              Text('النوع: ${debt.typeDisplayName}'),
              Text('الحالة: ${debt.statusDisplayName}'),
              if (debt.dueDate != null)
                Text('تاريخ الاستحقاق: ${_formatDate(debt.dueDate!)}'),
              Text('تاريخ الإنشاء: ${_formatDate(debt.createdAt)}'),
              if (debt.reference != null) Text('المرجع: ${debt.reference}'),
              if (debt.payments.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'المدفوعات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...debt.payments.map(
                  (payment) => Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Text(
                      '${payment.amount.toStringAsFixed(2)} ر.س - ${payment.methodDisplayName}',
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showPaymentDialog(Debt debt) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم فتح نافذة دفع للدين ${debt.id}')),
    );
  }

  void _showEditDebtDialog(Debt debt) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم فتح نافذة تعديل الدين ${debt.id}')),
    );
  }

  void _showDeleteConfirmation(Debt debt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الدين ${debt.id}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                debts.removeWhere((d) => d.id == debt.id);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الدين بنجاح')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
