<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class Sas4DynamicService
{
    /**
     * الحصول على إعدادات التشفير من ملف الإعدادات
     */
    private function getEncryptionMethod(): string
    {
        return config('sas4.encryption.method', 'AES-256-CBC');
    }

    private function getEncryptionKey(): string
    {
        return config('sas4.encryption.key', 'your-32-character-secret-key-here');
    }

    private function getEncryptionIV(): string
    {
        return config('sas4.encryption.iv', 'your-16-char-iv');
    }

    private function getConnectionTimeout(): int
    {
        return config('sas4.connection.timeout', 30);
    }

    private function getReadTimeout(): int
    {
        return config('sas4.connection.read_timeout', 60);
    }

    /**
     * تشفير البيانات بنفس طريقة SAS4
     */
    public function encryptPayload(array $data): string
    {
        try {
            $jsonData = json_encode($data);

            // استخدام إعدادات التشفير من ملف الإعدادات
            $key = hash('sha256', $this->getEncryptionKey(), true);
            $iv = substr(hash('sha256', $this->getEncryptionIV(), true), 0, 16);

            $encrypted = openssl_encrypt(
                $jsonData,
                $this->getEncryptionMethod(),
                $key,
                OPENSSL_RAW_DATA,
                $iv
            );

            if ($encrypted === false) {
                throw new Exception('فشل في تشفير البيانات');
            }

            return base64_encode($encrypted);

        } catch (Exception $e) {
            Log::error('خطأ في تشفير payload: ' . $e->getMessage());
            throw new Exception('خطأ في تشفير البيانات: ' . $e->getMessage());
        }
    }

    /**
     * فك تشفير البيانات
     */
    public function decryptPayload(string $encryptedData): array
    {
        try {
            $key = hash('sha256', $this->getEncryptionKey(), true);
            $iv = substr(hash('sha256', $this->getEncryptionIV(), true), 0, 16);

            $decrypted = openssl_decrypt(
                base64_decode($encryptedData),
                $this->getEncryptionMethod(),
                $key,
                OPENSSL_RAW_DATA,
                $iv
            );

            if ($decrypted === false) {
                throw new Exception('فشل في فك تشفير البيانات');
            }

            return json_decode($decrypted, true);

        } catch (Exception $e) {
            Log::error('خطأ في فك تشفير payload: ' . $e->getMessage());
            throw new Exception('خطأ في فك تشفير البيانات: ' . $e->getMessage());
        }
    }

    /**
     * تسجيل الدخول إلى لوحة SAS4
     */
    public function authenticateWithSas4(string $sas4Ip, string $username, string $password): array
    {
        try {
            $startTime = microtime(true);

            // إنشاء payload مشفر
            $payload = $this->encryptPayload([
                'username' => $username,
                'password' => $password,
                'timestamp' => time(),
            ]);

            // إرسال طلب تسجيل الدخول
            $response = Http::timeout($this->getConnectionTimeout())
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => config('sas4.connection.user_agent', 'AbrajiAPIs-SAS4-Client/1.0'),
                ])
                ->post("http://{$sas4Ip}/api/auth/login", [
                    'payload' => $payload,
                ]);

            $connectionTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['token']) || isset($data['access_token'])) {
                    return [
                        'success' => true,
                        'token' => $data['token'] ?? $data['access_token'],
                        'user_data' => $data['user'] ?? null,
                        'connection_time' => $connectionTime,
                        'response_data' => $data,
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'لم يتم العثور على token في الاستجابة',
                        'details' => $data,
                        'connection_time' => $connectionTime,
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'error' => 'فشل في تسجيل الدخول',
                    'status_code' => $response->status(),
                    'details' => $response->body(),
                    'connection_time' => $connectionTime,
                ];
            }

        } catch (Exception $e) {
            Log::error("خطأ في المصادقة مع SAS4 {$sas4Ip}: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'خطأ في الاتصال',
                'details' => $e->getMessage(),
                'connection_time' => 0,
            ];
        }
    }

    /**
     * جلب البيانات من endpoint محدد باستخدام token
     */
    public function fetchDataWithToken(string $sas4Ip, string $token, string $endpoint): array
    {
        try {
            $startTime = microtime(true);

            // تنظيف endpoint
            $endpoint = ltrim($endpoint, '/');
            
            $response = Http::timeout($this->getReadTimeout())
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->get("http://{$sas4Ip}/api/{$endpoint}");

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'endpoint' => $endpoint,
                    'response_time' => $responseTime,
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'فشل في جلب البيانات',
                    'status_code' => $response->status(),
                    'details' => $response->body(),
                    'response_time' => $responseTime,
                ];
            }

        } catch (Exception $e) {
            Log::error("خطأ في جلب البيانات من SAS4 {$sas4Ip}/{$endpoint}: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'خطأ في الاتصال',
                'details' => $e->getMessage(),
                'response_time' => 0,
            ];
        }
    }

    /**
     * تسجيل الدخول وجلب البيانات في عملية واحدة
     */
    public function authenticateAndFetchData(string $sas4Ip, string $username, string $password, string $endpoint): array
    {
        // تسجيل الدخول أولاً
        $authResult = $this->authenticateWithSas4($sas4Ip, $username, $password);
        
        if (!$authResult['success']) {
            return $authResult;
        }

        // جلب البيانات باستخدام token
        $dataResult = $this->fetchDataWithToken($sas4Ip, $authResult['token'], $endpoint);
        
        return [
            'success' => $dataResult['success'],
            'token' => $authResult['token'],
            'data' => $dataResult['data'] ?? null,
            'error' => $dataResult['error'] ?? null,
            'details' => $dataResult['details'] ?? null,
            'connection_time' => $authResult['connection_time'],
            'response_time' => $dataResult['response_time'] ?? 0,
        ];
    }

    /**
     * اختبار الاتصال (تسجيل دخول فقط)
     */
    public function testConnection(string $sas4Ip, string $username, string $password): array
    {
        $result = $this->authenticateWithSas4($sas4Ip, $username, $password);
        
        return [
            'success' => $result['success'],
            'message' => $result['success'] ? 'تم الاتصال بنجاح' : 'فشل في الاتصال',
            'connection_time' => $result['connection_time'],
            'error' => $result['error'] ?? null,
            'details' => $result['details'] ?? null,
        ];
    }

    /**
     * الحصول على قائمة endpoints المتاحة
     */
    public function getAvailableEndpoints(): array
    {
        return [
            'dashboard' => 'لوحة التحكم الرئيسية',
            'users/table' => 'جدول المستخدمين',
            'users/online' => 'المستخدمين المتصلين',
            'users/statistics' => 'إحصائيات المستخدمين',
            'reports/daily' => 'التقارير اليومية',
            'reports/monthly' => 'التقارير الشهرية',
            'system/status' => 'حالة النظام',
            'system/logs' => 'سجلات النظام',
            'billing/summary' => 'ملخص الفواتير',
            'network/status' => 'حالة الشبكة',
        ];
    }
}
