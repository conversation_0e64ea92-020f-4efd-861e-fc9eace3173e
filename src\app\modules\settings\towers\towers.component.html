<div appFlowbiteInit class="overflow-x-auto mt-4">
  <section class="bg-gray-50 dark:bg-gray-900">
    <div class="mx-auto max-w-screen-xl">
      <!-- Start coding here -->
      <div class="bg-white dark:bg-gray-800 relative shadow-xl border-2 sm:rounded-lg">
        <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div class="w-full md:w-1/2">
            <div class="flex items-center">
              <label for="simple-search" class="sr-only">{{'table.search' | transloco}}</label>
              <div class="relative w-full">
                <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
                  <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                    viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <input type="text" id="simple-search"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full ps-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                  placeholder="{{'table.search' | transloco}}..." required="">
              </div>
            </div>
          </div>

          <div
            class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
            <div class="flex items-center space-x-3 w-full md:w-auto">
              <!-- Modal toggle -->
              <button data-modal-target="create-expenses-modal" data-modal-toggle="create-expenses-modal"
                class="w-full text-white md:w-auto flex items-center justify-center mx-2 py-2 px-4 text-sm font-medium bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                <svg class="-ml-1 me-1.5 w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                  height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                    d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                </svg>
                {{ 'resources.addExpenses' | transloco }}
              </button>
              <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown"
                class="w-full md:w-auto flex items-center justify-center me-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                type="button">
                {{'table.actions' | transloco}}
                <svg class="-ml-1 ms-1.5 w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path clip-rule="evenodd" fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                </svg>
              </button>
              <div id="actionsDropdown"
                class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                  <li>
                    <a href="#"
                      class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Export</a>
                  </li>
                </ul>
                <div class="py-1">
                  <a href="#"
                    class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Delete
                    all</a>
                </div>
              </div>
              <button id="ColumnsDropdownButton" data-dropdown-toggle="ColumnsDropdown"
                class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                type="button">
                <svg class="h-4 w-4 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                </svg>
                {{'table.columns' | transloco}}
                <svg class="-mr-1 ms-1.5 w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path clip-rule="evenodd" fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                </svg>
              </button>
              <div id="ColumnsDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.showColumns' | transloco}}
                </h6>
                <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                  <!-- <li *ngFor="let column of expensesColumnsState" class="flex items-center">
                    <input title="{{column.key}}" type="checkbox" [id]="column.key" [checked]="!column.hidden"
                      (change)="toggleColumnSelection(column.key)"
                      class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                    <label [for]="column.key" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ ('resources.' + column.key) | transloco | titlecase }}
                    </label>
                  </li> -->
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
              <!-- <tr>
                    <th scope="col" class="px-2 py-3">#</th>
                    <ng-container *ngFor="let column of expensesColumnsState">
                      <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                        <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                          {{ ('resources.' + column.key) | transloco | titlecase }}
                          <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                            class="w-3 h-3 ms-1.5">
                            <path _ngcontent-ng-c3067077598=""
                              d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                            </path>
                          </svg>
                        </div>
                      </th>
                    </ng-container>
                    <th scope="col" class="px-4 py-3">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr> -->
              <tr>
                <th scope="col" class="px-2 py-3">#</th>
                <!-- <ng-container *ngFor="let column of expensesColumnsState">
                  <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                    @if (false) {
                    <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                      {{ ('resources.' + column.key) | transloco | titlecase }}
                      <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                        class="w-3 h-3 ms-1.5">
                        <path _ngcontent-ng-c3067077598=""
                          d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                        </path>
                      </svg>
                    </div>
                    }@else {
                    {{ ('resources.' + column.key) | transloco | titlecase }}
                    }
                  </th>
                </ng-container> -->
                <th scope="col" class="px-4 py-3">
                  <span scope="col"> {{'table.actions' | transloco}} </span>
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- <tr *ngFor="let item of tableResponse.data; let i = index" class="border-b dark:border-gray-700">
                <td class="px-4 py-3">{{ tableResponse.from + i }}</td>
                <ng-container *ngFor="let column of expensesColumnsState">
                  <td class="px-4 py-3" *ngIf="!column.hidden">
                    <ng-container>
                      @if (column.key === 'date' || column.key === 'created_at' || column.key === 'updated_at') {
                      {{ getPropertyValue(item, column.key) | date:'medium' }}
                      }@else if(column.key === 'type') {
                      @if(item.type === 'in') {

                      <span
                        class="flex items-center bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300"><span
                          class="me-2">
                          <svg class="w-6 h-6 text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                          </svg>
                        </span>
                        <span>
                          {{ getPropertyValue(item, column.key) }}
                        </span>
                      </span>
                      }@else {
                      <span
                        class="flex items-center bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300"><span
                          class="me-2">
                          <svg class="w-6 h-6 text-red-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                          </svg>
                        </span>
                        <span>
                          {{ getPropertyValue(item, column.key) }}
                        </span>
                      </span>
                      }

                      }@else {
                      {{ getPropertyValue(item, column.key) }}
                      }
                    </ng-container>
                  </td>
                </ng-container>
                <td class="px-4 py-3">
                  <div class="flex justify-between">
                    <div>
                      <button (click)="selectExpense(item)" data-modal-target="edit-expenses-modal"
                        data-modal-toggle="edit-expenses-modal" data-tooltip-target="tooltip-edit" type="button"
                        title="{{'resources.edit' | transloco}}">
                        <svg class="w-6 h-6 text-blue-500 dark:text-white" aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
                        </svg>

                      </button>
                      <div id="tooltip-edit" role="tooltip"
                        class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                        {{'resources.edit' | transloco}}
                        <div class="tooltip-arrow" data-popper-arrow></div>
                      </div>
                    </div>
                  </div>

                </td>
              </tr> -->
            </tbody>
          </table>
        </div>

      </div>
    </div>
  </section>
</div>