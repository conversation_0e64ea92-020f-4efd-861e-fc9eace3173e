import { Component, inject, input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as CryptoJS from 'crypto-js';
import { EncryptionService } from '../../../core/common-services/services/encryption.service';

@Component({
  selector: 'app-user-qr',
  templateUrl: './user-qr.component.html',
  styleUrl: './user-qr.component.scss',
})
export class UserQrComponent {
  userId: string | null = '';

  constructor(
    private route: ActivatedRoute,
    private encService: EncryptionService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const hashedId = params.get('hashedId');
      if (hashedId) {
        this.userId = this.encService.decryptQR(hashedId);
      }
    });
  }
  printPage() {
    window.print();
  }
}
