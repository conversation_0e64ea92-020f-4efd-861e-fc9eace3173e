import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../shared/widgets/custom_text_field.dart';

class SasConnectionCard extends StatefulWidget {
  const SasConnectionCard({super.key});

  @override
  State<SasConnectionCard> createState() => _SasConnectionCardState();
}

class _SasConnectionCardState extends State<SasConnectionCard> {
  final _formKey = GlobalKey<FormState>();
  final _sasUrlController = TextEditingController();
  final _sasIpController = TextEditingController();
  final _sasPortController = TextEditingController();
  bool _isLoading = false;
  bool _isConnected = false;

  @override
  void dispose() {
    _sasUrlController.dispose();
    _sasIpController.dispose();
    _sasPortController.dispose();
    super.dispose();
  }

  Future<void> _connectToSas() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // محاكاة الاتصال
    await Future.delayed(const Duration(seconds: 2));

    // تحديد URL الاتصال
    String? connectionUrl;
    if (_sasUrlController.text.isNotEmpty) {
      connectionUrl = _sasUrlController.text;
    } else if (_sasIpController.text.isNotEmpty) {
      final port = _sasPortController.text.isNotEmpty
          ? _sasPortController.text
          : "1812";
      connectionUrl = 'http://${_sasIpController.text}:$port';
    }

    if (connectionUrl != null) {
      // محاكاة نجاح الاتصال
      final success = true;

      setState(() {
        _isLoading = false;
        _isConnected = success;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاتصال بـ SAS Radius بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } else {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى إدخال URL أو IP للاتصال'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _disconnect() {
    setState(() {
      _isConnected = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم قطع الاتصال بـ SAS Radius'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان مع حالة الاتصال
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.router,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ربط SAS Radius',
                          style: AppTypography.titleLarge.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: _isConnected
                                    ? AppColors.success
                                    : AppColors.error,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              _isConnected ? 'متصل' : 'غير متصل',
                              style: AppTypography.bodySmall.copyWith(
                                color: _isConnected
                                    ? AppColors.success
                                    : AppColors.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // حقول الإدخال
              Text(
                'إعدادات الاتصال',
                style: AppTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 16),

              // URL الكامل
              CustomTextField(
                controller: _sasUrlController,
                label: 'SAS Radius URL (اختياري)',
                hint: 'http://*************:1812',
                prefixIcon: Icons.link,
                keyboardType: TextInputType.url,
              ),

              const SizedBox(height: 16),

              // أو استخدام IP منفصل
              Text(
                'أو استخدم IP منفصل:',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: CustomTextField(
                      controller: _sasIpController,
                      label: 'IP Address',
                      hint: '*************',
                      prefixIcon: Icons.computer,
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _sasPortController,
                      label: 'Port',
                      hint: '1812',
                      prefixIcon: Icons.settings_ethernet,
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                children: [
                  if (_isConnected) ...[
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _disconnect,
                        icon: const Icon(Icons.link_off),
                        label: const Text('قطع الاتصال'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.error,
                          side: BorderSide(color: AppColors.error),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _connectToSas,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Icon(_isConnected ? Icons.refresh : Icons.link),
                      label: Text(_isConnected ? 'إعادة الاتصال' : 'اتصال'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
