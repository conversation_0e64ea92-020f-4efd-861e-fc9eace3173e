<div class="flex items-center justify-center min-h-screen">
  <div class="text-center justify-center">
    <qr-code [value]="userId!" size="500" errorCorrectionLevel="M">
    </qr-code>
    <div>
      <p class="flex flex-row justify-center items-center font-semibold my-4  text-center">{{
        'common.poweredBy'|transloco }} <app-logo-icon class="ms-4 me-2" style="width: 2rem;" ></app-logo-icon> <app-logo-word></app-logo-word></p>
    </div>
  </div>
  <button class="fixed end-6 bottom-6 group print:hidden" (click)="printPage()">
    <div id="speed-dial-menu-text-inside-button-square" class="flex flex-col items-center mb-4 space-y-2">
      <button type="button"
        class="w-[56px] h-[56px] text-white bg-colors-main-color rounded-lg border border-gray-400 shadow-xl dark:border-gray-600 transition-all hover:bg-colors-secondary-color dark:bg-gray-700 dark:hover:bg-gray-600 focus:ring-2 focus:ring-violet-900 focus:outline-none dark:focus:ring-gray-400">
        <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
          viewBox="0 0 20 20">
          <path d="M5 20h10a1 1 0 0 0 1-1v-5H4v5a1 1 0 0 0 1 1Z" />
          <path
            d="M18 7H2a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2v-3a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v3a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2Zm-1-2V2a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3h14Z" />
        </svg>
        <span class="block mb-px text-xs font-medium">{{ 'common.print' | transloco }}</span>
      </button>
    </div>
  </button>
</div>
