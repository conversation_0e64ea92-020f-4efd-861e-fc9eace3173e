import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/setting_model.dart';

class SettingsApiService {
  static const String baseUrl = 'http://localhost:8000/api'; // يمكن تغييرها حسب البيئة
  
  // Headers للطلبات
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // يمكن إضافة Authorization header هنا
  };

  // جلب جميع الإعدادات
  Future<SettingsResponse> getAllSettings() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/settings'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return SettingsResponse.fromJson(jsonData);
      } else {
        throw Exception('فشل في جلب الإعدادات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // جلب إعداد محدد بالمفتاح
  Future<Setting?> getSettingByKey(String key) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/settings/$key'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return Setting.fromJson(jsonData['data']);
        }
        return null;
      } else if (response.statusCode == 404) {
        return null; // الإعداد غير موجود
      } else {
        throw Exception('فشل في جلب الإعداد: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // تحديث أو إنشاء إعداد
  Future<Setting> updateSetting(UpdateSettingRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/settings'),
        headers: _headers,
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return Setting.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? 'فشل في تحديث الإعداد');
        }
      } else {
        final jsonData = json.decode(response.body);
        throw Exception(jsonData['message'] ?? 'فشل في تحديث الإعداد');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // تحديث عدة إعدادات دفعة واحدة
  Future<List<Setting>> updateMultipleSettings(List<UpdateSettingRequest> requests) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/settings/bulk'),
        headers: _headers,
        body: json.encode({
          'settings': requests.map((r) => r.toJson()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return (jsonData['data'] as List)
              .map((item) => Setting.fromJson(item))
              .toList();
        } else {
          throw Exception(jsonData['message'] ?? 'فشل في تحديث الإعدادات');
        }
      } else {
        final jsonData = json.decode(response.body);
        throw Exception(jsonData['message'] ?? 'فشل في تحديث الإعدادات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // حذف إعداد
  Future<bool> deleteSetting(String key) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/settings/$key'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // جلب URL SAS Radius المناسب
  Future<String?> getSasRadiusUrl() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/sas-radius-url'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success']) {
          return jsonData['url'];
        }
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في جلب URL: $e');
    }
  }

  // اختبار الاتصال بـ SAS Radius
  Future<bool> testSasRadiusConnection(String url) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/test-sas-radius'),
        headers: _headers,
        body: json.encode({'url': url}),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // جلب الإعدادات العامة فقط
  Future<List<Setting>> getPublicSettings() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/settings/public'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return (jsonData['data'] as List)
              .map((item) => Setting.fromJson(item))
              .toList();
        }
      }
      return [];
    } catch (e) {
      throw Exception('خطأ في جلب الإعدادات العامة: $e');
    }
  }

  // تصدير الإعدادات
  Future<Map<String, dynamic>> exportSettings() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/settings/export'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('فشل في تصدير الإعدادات');
      }
    } catch (e) {
      throw Exception('خطأ في تصدير الإعدادات: $e');
    }
  }

  // استيراد الإعدادات
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/settings/import'),
        headers: _headers,
        body: json.encode(settings),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      }
      return false;
    } catch (e) {
      throw Exception('خطأ في استيراد الإعدادات: $e');
    }
  }
}
