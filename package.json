{"name": "abraji-web", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@jsverse/transloco": "^7.4.2", "abraji-web": "file:", "angular-highcharts": "^17.0.1", "chart.js": "^4.4.3", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "flowbite": "^2.4.1", "highcharts": "^11.4.6", "jwt-decode": "^4.0.0", "ng-qrcode": "^18.0.0", "ng2-charts": "^6.0.1", "ngx-filesaver": "^18.0.0", "primeicons": "^7.0.0", "primeng": "^17.18.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.6", "@angular/cli": "^18.0.6", "@angular/compiler-cli": "^18.0.0", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.19", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.39", "tailwindcss": "^3.4.4", "typescript": "~5.4.2"}}